import React from 'react';
import { LazyImage } from './LazyImage';

interface ImageWithCaptionProps {
  src: string;
  title?: string;
  source?: string;
  alt?: string;
  className?: string;
}

export const ImageWithCaption: React.FC<ImageWithCaptionProps> = ({
  src,
  title,
  source,
  alt,
  className = ""
}) => {
  return (
    <figure className={`my-8 text-center ${className}`}>
      <div className="inline-block max-w-full">
        <LazyImage
          src={src}
          alt={alt || title || "Imagem médica"}
          className="max-w-full h-auto rounded-lg shadow-md border border-gray-200"
        />
        
        {(title || source) && (
          <figcaption className="mt-3 text-sm text-gray-600 max-w-2xl mx-auto">
            {title && (
              <div className="font-semibold text-gray-800 mb-1">
                {title}
              </div>
            )}
            {source && (
              <div className="italic text-gray-500">
                {source}
              </div>
            )}
          </figcaption>
        )}
      </div>
    </figure>
  );
};

// Função para parsear o markdown customizado
export const parseImageWithCaption = (props: any) => {
  const { src, title, source, alt } = props;
  return (
    <ImageWithCaption
      src={src}
      title={title}
      source={source}
      alt={alt}
    />
  );
};
