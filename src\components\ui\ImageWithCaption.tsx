import React from 'react';
import { LazyImage } from './LazyImage';
import { ImageModal } from './ImageModal';

interface ImageWithCaptionProps {
  src: string;
  title?: string;
  source?: string;
  alt?: string;
  className?: string;
}

export const ImageWithCaption: React.FC<ImageWithCaptionProps> = ({
  src,
  title,
  source,
  alt,
  className = ""
}) => {
  return (
    <figure className={`my-8 text-center ${className}`}>
      <div className="inline-block max-w-full">
        <ImageModal
          src={src}
          title={title}
          source={source}
          alt={alt || title || "Imagem médica"}
        >
          <div className="cursor-pointer group relative">
            <LazyImage
              src={src}
              alt={alt || title || "Imagem médica"}
              className="max-w-full h-auto rounded-lg shadow-md border border-gray-200 transition-all duration-200 group-hover:shadow-lg group-hover:scale-[1.02]"
            />
            {/* Indicador de clique */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
              <div className="bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-gray-700 shadow-lg">
                Clique para expandir
              </div>
            </div>
          </div>
        </ImageModal>

        {(title || source) && (
          <figcaption className="mt-3 text-sm text-gray-600 max-w-2xl mx-auto">
            {title && (
              <div className="font-semibold text-gray-800 mb-1">
                {title}
              </div>
            )}
            {source && (
              <div className="italic text-gray-500">
                {source}
              </div>
            )}
          </figcaption>
        )}
      </div>
    </figure>
  );
};

// Função para parsear o markdown customizado
export const parseImageWithCaption = (props: any) => {
  const { src, title, source, alt } = props;
  return (
    <ImageWithCaption
      src={src}
      title={title}
      source={source}
      alt={alt}
    />
  );
};
