
import React, { useEffect, useState } from "react";
import { useParams, Link } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { ChevronLeft, Folder } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import FilterHeader from "@/components/filters/FilterHeader";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
} from "@/components/ui/dialog";
import { FeedbackPage } from "@/pages/feedback/FeedbackPage";
import { calculateLevenshteinDistance, removeMarkdown } from "@/lib/utils";
import { TopicCard } from "@/components/conducts/TopicCard";

interface Topic {
  id: string;
  name: string;
  slug: string;
  category_name: string;
  is_subcategory?: boolean;
  parent_id?: string;
  summary_count?: number; // Keep for sorting but don't display
}

export const ConductsTopicList = () => {
  const { categorySlug, subcategorySlug } = useParams();
  const [topics, setTopics] = useState<Topic[]>([]);
  const [categoryName, setCategoryName] = useState("");
  const [subcategoryName, setSubcategoryName] = useState("");
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showFeedback, setShowFeedback] = useState(false);
  const { toast } = useToast();

  // Função para normalizar texto (remover acentos e converter para minúsculas)
  const normalizeText = (text: string) => {
    return text
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '');
  };

  useEffect(() => {
    const fetchTopics = async () => {
      try {
        // Get the category name first
        const { data: categoryData, error: categoryError } = await supabase
          .from('pedbook_conducts_categories')
          .select('name')
          .eq('slug', categorySlug)
          .single();

        if (categoryError) throw categoryError;

        if (categoryData) {
          setCategoryName(categoryData.name);
        }

        let topicsData;
        let error;

        if (subcategorySlug) {
          // Buscar subcategoria
          const { data: subcategoryData } = await supabase
            .from('v_conducts_topics')
            .select('id, name')
            .eq('category_slug', categorySlug)
            .eq('slug', subcategorySlug)
            .eq('is_subcategory', true)
            .single();

          if (subcategoryData) {
            setSubcategoryName(subcategoryData.name);

            // Buscar tópicos filhos
            const { data: childTopicsData, error: childError } = await supabase
              .from('v_conducts_topics')
              .select('id, name, slug, category_name, is_subcategory, parent_id')
              .eq('parent_id', subcategoryData.id);

            topicsData = childTopicsData;
            error = childError;
          }
        } else {
          // Se não há subcategoria, buscar apenas subcategorias e tópicos diretos da categoria
          const { data: directTopicsData, error: directError } = await supabase
            .from('v_conducts_topics')
            .select('id, name, slug, category_name, is_subcategory, parent_id')
            .eq('category_slug', categorySlug)
            .is('parent_id', null); // Apenas tópicos diretos (sem parent_id)

          topicsData = directTopicsData;
          error = directError;
        }

        if (error) throw error;

        if (topicsData && topicsData.length > 0) {
          // For each topic, count how many PUBLISHED summaries it has (only for non-subcategories)
          const topicsWithSummaryCount = await Promise.all(
            topicsData.map(async (topic) => {
              // Se é subcategoria, não precisa contar resumos
              if (topic.is_subcategory) {
                return { ...topic, summary_count: 0 };
              }

              // Count published summaries from both tables
              const [
                { count: standardCount, error: standardError },
                { count: optimizedCount, error: optimizedError }
              ] = await Promise.all([
                supabase
                  .from('pedbook_conducts_summaries')
                  .select('*', { count: 'exact', head: true })
                  .eq('topic_id', topic.id)
                  .eq('published', true),
                supabase
                  .from('pedbook_conducts_optimized')
                  .select('*', { count: 'exact', head: true })
                  .eq('topic_id', topic.id)
                  .eq('published', true)
              ]);

              if (standardError && optimizedError) {
                return { ...topic, summary_count: 0 };
              }

              const totalCount = (standardCount || 0) + (optimizedCount || 0);
              return { ...topic, summary_count: totalCount };
            })
          );
          
          // Filter: include subcategories OR topics with published summaries
          const topicsWithPublishedContent = topicsWithSummaryCount.filter(topic =>
            topic.is_subcategory || (topic.summary_count || 0) > 0
          );

          const sortedTopics = topicsWithPublishedContent.sort((a, b) => {
            // First, subcategories come before regular topics
            if (a.is_subcategory && !b.is_subcategory) return -1;
            if (!a.is_subcategory && b.is_subcategory) return 1;

            // If both are subcategories or both are topics, sort alphabetically
            if (a.is_subcategory && b.is_subcategory) {
              return a.name.localeCompare(b.name);
            }

            // For regular topics, sort by summary count (descending), then alphabetically
            const countDiff = (b.summary_count || 0) - (a.summary_count || 0);
            if (countDiff === 0) {
              return a.name.localeCompare(b.name);
            }

            return countDiff;
          });

          setTopics(sortedTopics);
        } else {
          toast({
            title: "Nenhum tópico encontrado",
            description: "Não existem tópicos cadastrados para esta categoria.",
            variant: "destructive",
          });
        }
      } catch (error: any) {
        console.error('Error fetching topics:', error);
        toast({
          title: "Erro ao carregar tópicos",
          description: error.message,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    if (categorySlug) {
      fetchTopics();
    }
  }, [categorySlug, subcategorySlug, toast]);

  const filteredTopics = topics.filter(topic => {
    const normalizedTopicName = normalizeText(topic.name);
    const normalizedSearchTerm = normalizeText(searchTerm);
    
    // Divide o termo de busca em palavras
    const searchWords = normalizedSearchTerm.split(' ');
    
    // Verifica se todas as palavras da busca estão presentes no nome do tópico
    return searchWords.every(word => normalizedTopicName.includes(word));
  });

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary dark:border-blue-400"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800">
      <Header />
      
      <main className="flex-1 container mx-auto px-2 py-6 md:px-4 md:py-8 overflow-hidden">
        <div className="max-w-3xl mx-auto space-y-7 md:space-y-9">
          <div className="flex items-center gap-3 md:gap-5">
            <Link to={subcategorySlug ? `/condutas-e-manejos/${categorySlug}` : "/condutas-e-manejos"}>
              <Button variant="ghost" size="icon" className="h-11 w-11 md:h-12 md:w-12 hover:bg-white/50 dark:hover:bg-slate-800/50">
                <ChevronLeft className="h-6 w-6" />
              </Button>
            </Link>
            <div className="flex-1">
              <h1 className="text-xl md:text-3xl lg:text-4xl font-bold gradient-text break-words">
                {subcategorySlug ? subcategoryName : categoryName}
              </h1>
              {subcategorySlug && (
                <p className="text-sm md:text-base text-gray-600 dark:text-gray-400 mt-1">
                  {categoryName} → {subcategoryName}
                </p>
              )}
            </div>
          </div>

          <div className="max-w-md mx-auto">
            <FilterHeader
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
            />
          </div>

          {/* Feedback button above topic list but more subtle */}
          <div className="text-center">
            <Button
              onClick={() => setShowFeedback(true)}
              variant="ghost"
              className="text-xs md:text-sm text-blue-500/70 hover:text-blue-600 dark:text-blue-400/70 dark:hover:text-blue-300 hover:bg-blue-50/50 dark:hover:bg-blue-900/20 py-1 px-3 rounded-md font-normal"
            >
              Falta algum resumo que gostaria de ver? Clique aqui
            </Button>
          </div>

          <div className="flex flex-col space-y-4">
            <ScrollArea className="h-[60vh] rounded-md">
              <div className="grid grid-cols-1 gap-3 pr-1 md:pr-4">
                {filteredTopics.map((topic) => (
                  <TopicCard
                    key={topic.id}
                    topic={topic}
                    categorySlug={categorySlug || ''}
                    subcategorySlug={subcategorySlug}
                    searchTerm={searchTerm}
                  />
                ))}
              </div>
            </ScrollArea>

            {filteredTopics.length === 0 && !loading && (
              <div className="text-center py-8 md:py-12">
                <h3 className="text-lg md:text-xl lg:text-2xl font-medium text-gray-600 dark:text-gray-300">
                  Nenhum tópico encontrado
                </h3>
                <p className="text-sm md:text-base lg:text-lg text-gray-500 dark:text-gray-400 mt-2">
                  Tente ajustar sua pesquisa ou explore outras categorias.
                </p>
              </div>
            )}
          </div>
        </div>
      </main>
      <Footer />

      <Dialog open={showFeedback} onOpenChange={setShowFeedback}>
        <DialogContent className="sm:max-w-[500px] dark:bg-slate-800 dark:border-slate-700">
          <FeedbackPage />
        </DialogContent>
      </Dialog>
    </div>
  );
};


