import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { ConductsTopicList } from "./ConductsTopicList";
import { ConductsSummary } from "./ConductsSummary";
import { Loader2 } from "lucide-react";

const ConductsRouter = () => {
  const { categorySlug, subcategorySlug, topicSlug } = useParams();
  const [loading, setLoading] = useState(true);
  const [showSummary, setShowSummary] = useState(false);

  useEffect(() => {
    const decide = async () => {
      try {
        // 3 parâmetros = sempre resumo
        if (topicSlug) {
          setShowSummary(true);
          setLoading(false);
          return;
        }

        // 1 parâmetro = sempre lista
        if (!subcategorySlug) {
          setShowSummary(false);
          setLoading(false);
          return;
        }

        // 2 parâmetros = verificar se é subcategoria
        const { data } = await supabase
          .from('v_conducts_topics')
          .select('is_subcategory')
          .eq('category_slug', categorySlug)
          .eq('slug', subcategorySlug)
          .maybeSingle();

        // Se não encontrou OU se is_subcategory é false/null = resumo
        // Se encontrou E is_subcategory é true = lista
        setShowSummary(!data?.is_subcategory);

      } catch (error) {
        console.error('Erro:', error);
        setShowSummary(false);
      } finally {
        setLoading(false);
      }
    };

    decide();
  }, [categorySlug, subcategorySlug, topicSlug]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
      </div>
    );
  }

  return showSummary ? <ConductsSummary /> : <ConductsTopicList />;
};

export default ConductsRouter;
