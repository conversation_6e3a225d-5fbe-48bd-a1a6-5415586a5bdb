import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";

const subcategorySchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  description: z.string().optional(),
  slug: z.string().optional(),
  icon: z.string().optional(),
  category_id: z.string().uuid("Categoria inválida"),
});

type SubcategoryFormData = z.infer<typeof subcategorySchema>;

interface SubcategoryDialogProps {
  subcategory?: {
    id: string;
    name: string;
    description: string | null;
    slug: string;
    icon: string | null;
    category_id: string;
  };
  categoryId: string;
  onOpenChange: (open: boolean) => void;
  open: boolean;
}

export function SubcategoryDialog({ subcategory, categoryId, onOpenChange, open }: SubcategoryDialogProps) {
  console.log('🔄 SubcategoryDialog renderizado', { isEditing: !!subcategory?.id, categoryId, open });

  const queryClient = useQueryClient();
  const { user } = useAuth();

  const form = useForm<SubcategoryFormData>({
    resolver: zodResolver(subcategorySchema),
    defaultValues: {
      name: "",
      description: "",
      slug: "",
      icon: "",
      category_id: categoryId,
    },
  });

  // Resetar formulário quando o dialog abrir/fechar ou quando a subcategoria mudar
  useEffect(() => {
    if (open) {
      if (subcategory) {
        console.log('🔄 Carregando dados da subcategoria para edição:', subcategory.id);
        form.reset({
          name: subcategory.name,
          description: subcategory.description || "",
          slug: subcategory.slug,
          icon: subcategory.icon || "",
          category_id: subcategory.category_id,
        });
      } else {
        console.log('🆕 Preparando formulário para nova subcategoria');
        form.reset({
          name: "",
          description: "",
          slug: "",
          icon: "",
          category_id: categoryId,
        });
      }
    }
  }, [open, subcategory, categoryId, form]);

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const onSubmit = async (data: SubcategoryFormData) => {
    try {
      console.log('💾 Salvando subcategoria:', data);

      if (!user) {
        toast({
          title: "Erro",
          description: "Usuário não autenticado",
          variant: "destructive",
        });
        return;
      }

      // Gerar slug se não fornecido
      const slugValue = data.slug || generateSlug(data.name);

      const payload = {
        name: data.name,
        description: data.description,
        slug: slugValue,
        icon: data.icon,
        category_id: data.category_id,
        parent_id: null, // Subcategorias não têm parent_id
        is_subcategory: true, // Marcar como subcategoria
        published: true,
      };

      let result;
      if (subcategory?.id) {
        // Atualizar subcategoria existente
        result = await supabase
          .from('pedbook_conducts_topics')
          .update(payload)
          .eq('id', subcategory.id)
          .select()
          .single();
      } else {
        // Criar nova subcategoria
        result = await supabase
          .from('pedbook_conducts_topics')
          .insert(payload)
          .select()
          .single();
      }

      if (result.error) {
        console.error('❌ Erro ao salvar subcategoria:', result.error);
        throw result.error;
      }

      console.log('✅ Subcategoria salva com sucesso:', result.data);

      queryClient.invalidateQueries({ queryKey: ['conducts-topics'] });
      
      // Mostrar toast de sucesso
      toast({
        title: "Sucesso",
        description: subcategory?.id ? "Subcategoria atualizada com sucesso!" : "Subcategoria criada com sucesso!",
      });
      
      // Fechar dialog
      handleClose();

    } catch (error: any) {
      console.error('❌ Error:', error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao salvar a subcategoria",
        variant: "destructive",
      });
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {subcategory?.id ? "Editar Subcategoria" : "Nova Subcategoria"}
          </DialogTitle>
          <DialogDescription>
            {subcategory?.id ? "Edite os dados da subcategoria" : "Crie uma nova subcategoria"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Ex: Doenças Exantemáticas" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição (opcional)</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Breve descrição da subcategoria" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="slug"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Slug (opcional)</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="doencas-exantematicas" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="icon"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ícone (opcional)</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="🦠" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancelar
            </Button>
            <Button type="submit">
              {subcategory?.id ? "Atualizar" : "Criar"} Subcategoria
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
