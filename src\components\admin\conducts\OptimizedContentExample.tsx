import React from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Co<PERSON>, Eye } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export const OptimizedContentExample = () => {
  const { toast } = useToast();

  const exampleConductsContent = {
    sections: [
      {
        id: "definicao",
        title: "Definição e Contexto Clínico",
        content: `A **icterícia neonatal** é a manifestação clínica da hiperbilirrubinemia, caracterizada pela coloração amarelada da pele e mucosas devido ao acúmulo de bilirrubina.

> **⚠️ ATENÇÃO:** Condição extremamente comum (60% dos RN a termo e 80% dos prematuros), mas que pode causar sequelas neurológicas graves se não adequadamente manejada.

**Epidemiologia crítica:**
- Icterícia visível: 60% RN termo, 80% prematuros
- Hiperbilirrubinemia significativa: 5-10% dos RN
- Kernicterus: <1/100.000 nascimentos
- Readmissão hospitalar: Principal causa nos primeiros 30 dias`,
        order: 1,
        show_in_menu: true
      },
      {
        id: "classificacao",
        title: "Classificação",
        content: `### Por Tempo de Aparecimento

| Tipo | Início | Características | Principais Causas |
|------|-------|-----------------|-------------------|
| **Precoce** | <24h | Sempre patológica | Hemólise, infecção |
| **Fisiológica** | 24-72h | Benigna, autolimitada | Imaturidade hepática |
| **Prolongada** | >2 semanas | Investigação obrigatória | Hipotireoidismo, atresia biliar |

### Por Tipo de Bilirrubina
- **Indireta (não-conjugada):** >85% dos casos neonatais
- **Direta (conjugada):** <20% da bilirrubina total (sempre patológica)`,
        order: 2,
        show_in_menu: true,
        subsections: [
          {
            id: "fatores-risco",
            title: "Fatores de Risco",
            content: `**Fatores de Risco Maior:**
- Icterícia nas primeiras 24h
- Incompatibilidade ABO/Rh
- Deficiência de G6PD
- Idade gestacional 35-38 semanas

**Fatores Protetivos:**
- Idade gestacional ≥41 semanas
- Aleitamento artificial
- Raça negra`
          }
        ]
      },
      {
        id: "avaliacao",
        title: "Avaliação Clínica",
        content: `### Inspeção Visual (Kramer)
- **Zona 1:** Face e pescoço (BT ~6 mg/dL)
- **Zona 2:** Tronco até umbigo (BT ~9 mg/dL)
- **Zona 3:** Tronco até joelhos (BT ~12 mg/dL)
- **Zona 4:** Braços e pernas (BT ~15 mg/dL)
- **Zona 5:** Palmas e plantas (BT >15 mg/dL)

### Sinais de Alarme
> **🚨 EMERGÊNCIA:** Icterícia nas primeiras 24h
> **⚠️ ATENÇÃO:** Progressão rápida (>5 mg/dL/dia)
> **⚠️ ATENÇÃO:** Sinais neurológicos (letargia, hipotonia)`,
        order: 3,
        show_in_menu: true
      }
    ],
    metadata: {
      last_updated: "2025-01-15",
      version: "1.0",
      author: "Sistema PedBook"
    }
  };

  const exampleTreatmentContent = {
    sections: [
      {
        id: "fototerapia",
        title: "Fototerapia",
        content: `### Mecanismo
Fotoisomerização da bilirrubina em produtos hidrossolúveis

### Técnica Adequada
- **Lâmpadas LED azuis** (460-490 nm) - padrão-ouro
- **Irradiância:** >30 μW/cm²/nm
- **Distância:** 10-30 cm da pele
- **Área exposta:** Máxima possível
- **Proteção ocular:** Obrigatória

### Monitorização
- **Bilirrubina:** 4-6h após início, depois 12-24h
- **Hidratação:** Aumentar 10-20% das necessidades
- **Temperatura:** Evitar hipertermia`,
        order: 1,
        show_in_menu: true
      },
      {
        id: "exsanguineotransfusao",
        title: "Exsanguineotransfusão",
        content: `### Indicações
- Falha da fototerapia intensiva
- Níveis críticos de bilirrubina
- Sinais de encefalopatia bilirrubínica
- Anemia grave com hemólise

### Técnica
- **Volume:** 2x volemia (160 mL/kg)
- **Sangue:** Compatível, <5 dias, irradiado
- **Via:** Cateter umbilical arterial e venoso
- **Velocidade:** 2-4 mL/kg/min`,
        order: 2,
        show_in_menu: true
      },
      {
        id: "seguimento",
        title: "Seguimento e Monitorização",
        content: `### Critérios de Suspensão da Fototerapia
- **Bilirrubina <13-14 mg/dL** (RN termo)
- **Tendência decrescente** por 12-24h
- **Ausência de hemólise** ativa
- **Alimentação adequada** estabelecida

### Seguimento Pós-Tratamento
- **Bilirrubina de rebote:** 12-24h após suspensão
- **Consulta ambulatorial:** 24-48h após alta
- **Avaliação neurológica:** Se tratamento intensivo`,
        order: 3,
        show_in_menu: true
      }
    ]
  };

  const exampleNavigationSections = [
    {
      id: "definicao",
      title: "O que é icterícia neonatal?",
      target_section: "conducts_content.sections[0]",
      order: 1,
      icon: "info"
    },
    {
      id: "classificacao",
      title: "Quais os tipos?",
      target_section: "conducts_content.sections[1]",
      order: 2,
      icon: "list"
    },
    {
      id: "avaliacao",
      title: "Como avaliar?",
      target_section: "conducts_content.sections[2]",
      order: 3,
      icon: "search"
    },
    {
      id: "fototerapia",
      title: "Como tratar?",
      target_section: "treatment_content.sections[0]",
      order: 4,
      icon: "treatment"
    },
    {
      id: "seguimento",
      title: "Como fazer seguimento?",
      target_section: "treatment_content.sections[2]",
      order: 5,
      icon: "follow-up"
    }
  ];

  const copyToClipboard = (content: any, type: string) => {
    navigator.clipboard.writeText(JSON.stringify(content, null, 2));
    toast({
      title: "Copiado!",
      description: `Conteúdo de ${type} copiado para a área de transferência.`
    });
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">Exemplo de Conteúdo Otimizado</h2>
        <p className="text-gray-600 dark:text-gray-400">
          Use estes exemplos como base para criar conteúdo no formato otimizado
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Condutas e Manejos */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Condutas e Manejos</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => copyToClipboard(exampleConductsContent, "Condutas")}
            >
              <Copy className="w-4 h-4 mr-2" />
              Copiar JSON
            </Button>
          </div>
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 max-h-96 overflow-y-auto">
            <pre className="text-xs">
              {JSON.stringify(exampleConductsContent, null, 2)}
            </pre>
          </div>
        </Card>

        {/* Tratamento */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Tratamento</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => copyToClipboard(exampleTreatmentContent, "Tratamento")}
            >
              <Copy className="w-4 h-4 mr-2" />
              Copiar JSON
            </Button>
          </div>
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 max-h-96 overflow-y-auto">
            <pre className="text-xs">
              {JSON.stringify(exampleTreatmentContent, null, 2)}
            </pre>
          </div>
        </Card>
      </div>

      {/* Navegação */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Seções de Navegação</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => copyToClipboard(exampleNavigationSections, "Navegação")}
          >
            <Copy className="w-4 h-4 mr-2" />
            Copiar JSON
          </Button>
        </div>
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <pre className="text-xs">
            {JSON.stringify(exampleNavigationSections, null, 2)}
          </pre>
        </div>
      </Card>

      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
          💡 Como usar:
        </h4>
        <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
          <li>1. Copie o JSON do exemplo desejado</li>
          <li>2. Cole no campo correspondente (Condutas ou Tratamento)</li>
          <li>3. Modifique o conteúdo conforme necessário</li>
          <li>4. Salve o tópico com formato "Otimizado"</li>
        </ul>
      </div>
    </div>
  );
};
