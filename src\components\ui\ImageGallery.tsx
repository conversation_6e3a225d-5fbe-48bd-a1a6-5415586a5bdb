import React from 'react';
import { ImageWithCaption } from './ImageWithCaption';

interface ImageData {
  src: string;
  title?: string;
  source?: string;
  alt?: string;
  global?: boolean;
}

interface ImageGalleryProps {
  images: ImageData[];
  className?: string;
}

export const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  className = ""
}) => {
  if (images.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
          <span className="text-4xl">🖼️</span>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          Nenhuma imagem global encontrada
        </h3>
        <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
          As imagens marcadas como globais aparecerão aqui em formato de galeria.
        </p>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      {/* Grid de imagens usando ImageWithCaption */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
        {images.map((image, index) => (
          <div key={index} className="transform hover:scale-105 transition-transform duration-200">
            <ImageWithCaption
              src={image.src}
              title={image.title}
              source={image.source}
              alt={image.alt}
              allImages={images}
              currentIndex={index}
              className="shadow-lg hover:shadow-xl transition-shadow duration-200"
            />
          </div>
        ))}
      </div>
    </div>
  );
};
