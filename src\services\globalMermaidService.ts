/**
 * Serviço Global para Mermaid
 * 
 * PROBLEMA RESOLVIDO: expandMermaidDiagram is not defined
 * 
 * SOLUÇÃO: Sistema global robusto que funciona independente de componentes montados
 */

class GlobalMermaidService {
  private static instance: GlobalMermaidService;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): GlobalMermaidService {
    if (!GlobalMermaidService.instance) {
      GlobalMermaidService.instance = new GlobalMermaidService();
    }
    return GlobalMermaidService.instance;
  }

  /**
   * Inicializa o serviço global de Mermaid
   * Deve ser chamado uma vez no início da aplicação
   */
  public initialize(): void {
    if (this.isInitialized) {
      return;
    }

    // 🛡️ FUNÇÃO GLOBAL DEFINITIVA - Sempre disponível
    (window as any).expandMermaidDiagram = (id: string) => {
      this.expandMermaidDiagram(id);
    };

    // 🛡️ FUNÇÃO GLOBAL PARA TABELAS - Sempre disponível
    (window as any).openTableDialog = (tableId: string) => {
      this.openTableDialog(tableId);
    };

    this.isInitialized = true;
  }

  /**
   * Expande um diagrama Mermaid usando eventos customizados
   */
  private expandMermaidDiagram(id: string): void {
    try {
      // 1. Tentar encontrar o código no script JSON
      const codeElement = document.getElementById(id + '-code');
      if (!codeElement) {
        console.warn('🚫 Elemento de código Mermaid não encontrado:', id);
        return;
      }

      // 2. Parse do código Mermaid
      let mermaidCode: string;
      try {
        mermaidCode = JSON.parse(codeElement.textContent || '');
      } catch (parseError) {
        console.error('🚫 Erro ao fazer parse do código Mermaid:', parseError);
        return;
      }

      // 3. 🎯 DISPARAR EVENTO CUSTOMIZADO - Funciona com qualquer componente
      const event = new CustomEvent('openMermaidModal', {
        detail: { mermaidCode },
        bubbles: true
      });

      document.dispatchEvent(event);

    } catch (error) {
      console.error('🚫 Erro ao expandir Mermaid:', error);
      
      // 🛡️ FALLBACK: Tentar abrir em nova aba se tudo falhar
      this.fallbackMermaidDisplay(id);
    }
  }

  /**
   * Abre dialog de tabela usando eventos customizados
   */
  private openTableDialog(tableId: string): void {
    try {
      // 1. Tentar encontrar os dados da tabela
      const dataElement = document.getElementById(`table-data-${tableId}`);
      if (!dataElement) {
        console.warn('🚫 Elemento de dados da tabela não encontrado:', tableId);
        return;
      }

      // 2. Parse dos dados da tabela
      let tableData: any[];
      try {
        tableData = JSON.parse(dataElement.textContent || '[]');
      } catch (parseError) {
        console.error('🚫 Erro ao fazer parse dos dados da tabela:', parseError);
        return;
      }

      // 3. 🎯 DISPARAR EVENTO CUSTOMIZADO
      const event = new CustomEvent('openTableModal', {
        detail: { tableData },
        bubbles: true
      });

      document.dispatchEvent(event);

    } catch (error) {
      console.error('🚫 Erro ao abrir tabela:', error);
    }
  }

  /**
   * Fallback para exibir Mermaid quando tudo falha
   */
  private fallbackMermaidDisplay(id: string): void {
    try {
      const codeElement = document.getElementById(id + '-code');
      if (codeElement) {
        const mermaidCode = JSON.parse(codeElement.textContent || '');
        
        // Abrir em nova janela como último recurso
        const newWindow = window.open('', '_blank', 'width=800,height=600');
        if (newWindow) {
          newWindow.document.write(`
            <html>
              <head>
                <title>Diagrama Mermaid</title>
                <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
              </head>
              <body>
                <div id="mermaid-container">
                  <pre class="mermaid">${mermaidCode}</pre>
                </div>
                <script>
                  mermaid.initialize({ startOnLoad: true });
                </script>
              </body>
            </html>
          `);
          newWindow.document.close();
        }
      }
    } catch (error) {
      console.error('🚫 Erro no fallback Mermaid:', error);
    }
  }

  /**
   * Cleanup - remove funções globais se necessário
   */
  public cleanup(): void {
    if (this.isInitialized) {
      delete (window as any).expandMermaidDiagram;
      delete (window as any).openTableDialog;
      this.isInitialized = false;
    }
  }
}

// 🎯 EXPORTAR INSTÂNCIA SINGLETON
export const globalMermaidService = GlobalMermaidService.getInstance();
