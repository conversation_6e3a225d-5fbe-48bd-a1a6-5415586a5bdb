import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  fallback?: string;
  blur?: boolean;
  priority?: boolean;
  onLoad?: () => void;
  onError?: () => void;
  width?: number;
  height?: number;
}

/**
 * Hook para Intersection Observer otimizado com logs reduzidos
 */
const useIntersectionObserver = (
  ref: React.RefObject<HTMLElement>,
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const hasLoggedSetupRef = useRef(false);

  useEffect(() => {
    const element = ref.current;
    if (!element || isIntersecting) return;

    // Log apenas uma vez por elemento
    if (!hasLoggedSetupRef.current) {
      hasLoggedSetupRef.current = true;
      console.log(`👁️ Configurando Intersection Observer:`, {
        element: element.tagName,
        timestamp: new Date().toISOString()
      });
    }

    // Reutilizar observer se possível
    if (!observerRef.current) {
      observerRef.current = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            console.log(`✨ Elemento entrou na viewport`);
            setIsIntersecting(true);
            observerRef.current?.unobserve(element);
          }
        },
        {
          rootMargin: '50px',
          threshold: 0.1,
          ...options
        }
      );
    }

    observerRef.current.observe(element);

    return () => {
      if (observerRef.current && element) {
        observerRef.current.unobserve(element);
      }
    };
  }, [ref, isIntersecting]);

  // Cleanup no unmount
  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
        observerRef.current = null;
      }
    };
  }, []);

  return isIntersecting;
};

/**
 * Função para verificar o tamanho do arquivo de imagem
 */
const checkImageSize = async (url: string): Promise<number | null> => {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    const contentLength = response.headers.get('content-length');
    return contentLength ? parseInt(contentLength, 10) : null;
  } catch (error) {
    console.error(`❌ Erro ao verificar tamanho da imagem: ${url}`, error);
    return null;
  }
};

/**
 * Componente LazyImage otimizado com progressive loading
 */
const LazyImageComponent: React.FC<LazyImageProps> = ({
  src,
  alt,
  className,
  fallback = '/faviconx.webp',
  blur = true,
  priority = false,
  onLoad,
  onError,
  width,
  height,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [imageSrc, setImageSrc] = useState<string>('');
  const [loadStartTime, setLoadStartTime] = useState<number | null>(null);
  const imgRef = useRef<HTMLDivElement>(null);

  // Log inicial detalhado (apenas uma vez por src)
  const hasLoggedRef = useRef(new Set<string>());

  useEffect(() => {
    if (!hasLoggedRef.current.has(src)) {
      hasLoggedRef.current.add(src);
      console.log(`🖼️ LazyImage iniciado:`, {
        src,
        alt: alt || 'sem alt',
        isWebP: src.includes('.webp'),
        priority,
        timestamp: new Date().toISOString()
      });

      // Verificar tamanho do arquivo (apenas uma vez)
      const checkSize = async () => {
        const size = await checkImageSize(src);
        if (size) {
          const sizeInKB = (size / 1024).toFixed(2);
          const sizeInMB = (size / (1024 * 1024)).toFixed(2);
          console.log(`📏 Tamanho da imagem:`, {
            src,
            bytes: size,
            kb: `${sizeInKB} KB`,
            mb: `${sizeInMB} MB`,
            isLarge: size > 1024 * 1024, // > 1MB
            timestamp: new Date().toISOString()
          });
        }
      };

      checkSize();
    }
  }, [src, alt, priority]);
  
  // Usar intersection observer apenas se não for priority
  const isInView = useIntersectionObserver(imgRef, {
    rootMargin: '100px'
  });

  const shouldLoad = priority || isInView;

  // Log mudanças de estado (apenas mudanças significativas)
  const prevStateRef = useRef({ shouldLoad: false, isLoaded: false, hasError: false });

  useEffect(() => {
    const currentState = { shouldLoad, isLoaded, hasError };
    const prevState = prevStateRef.current;

    // Só logar se houve mudança significativa
    if (
      currentState.shouldLoad !== prevState.shouldLoad ||
      currentState.isLoaded !== prevState.isLoaded ||
      currentState.hasError !== prevState.hasError
    ) {
      console.log(`🔄 Estado da imagem mudou:`, {
        src: src.split('/').pop(), // Apenas nome do arquivo
        changes: {
          shouldLoad: `${prevState.shouldLoad} → ${currentState.shouldLoad}`,
          isLoaded: `${prevState.isLoaded} → ${currentState.isLoaded}`,
          hasError: `${prevState.hasError} → ${currentState.hasError}`
        },
        timestamp: new Date().toISOString()
      });

      prevStateRef.current = currentState;
    }
  }, [src, shouldLoad, isLoaded, hasError]);

  // Log informações de rede (apenas uma vez por sessão)
  useEffect(() => {
    if (typeof navigator !== 'undefined' && 'connection' in navigator && !hasLoggedRef.current.has('network')) {
      hasLoggedRef.current.add('network');
      const connection = (navigator as any).connection;
      console.log(`🌐 Informações de rede:`, {
        effectiveType: connection?.effectiveType,
        downlink: connection?.downlink,
        rtt: connection?.rtt,
        saveData: connection?.saveData,
        timestamp: new Date().toISOString()
      });
    }
  }, []);

  useEffect(() => {
    if (!shouldLoad || hasError) return;

    console.log(`⏳ Iniciando carregamento da imagem:`, {
      src,
      shouldLoad,
      hasError,
      isInView,
      priority,
      timestamp: new Date().toISOString()
    });

    const startTime = performance.now();
    setLoadStartTime(startTime);

    const img = new Image();

    img.onload = () => {
      const loadTime = performance.now() - startTime;
      console.log(`✅ Imagem carregada com sucesso:`, {
        src,
        loadTime: `${loadTime.toFixed(2)}ms`,
        naturalWidth: img.naturalWidth,
        naturalHeight: img.naturalHeight,
        timestamp: new Date().toISOString()
      });

      setImageSrc(src);
      setIsLoaded(true);
      onLoad?.();
    };

    img.onerror = (error) => {
      const loadTime = performance.now() - startTime;
      console.error(`❌ Erro ao carregar imagem:`, {
        src,
        error,
        loadTime: `${loadTime.toFixed(2)}ms`,
        fallback,
        willRetryWithFallback: src !== fallback,
        timestamp: new Date().toISOString()
      });

      setHasError(true);
      setImageSrc(fallback);
      onError?.();
    };

    // Log antes de iniciar o download
    console.log(`📥 Iniciando download da imagem: ${src}`);

    // Timeout para detectar carregamento lento
    const timeoutId = setTimeout(() => {
      const currentTime = performance.now() - startTime;
      console.warn(`⏰ Imagem demorou mais que 5s para carregar:`, {
        src,
        timeElapsed: `${currentTime.toFixed(2)}ms`,
        status: 'ainda carregando',
        timestamp: new Date().toISOString()
      });
    }, 5000);

    img.src = src;

    return () => {
      clearTimeout(timeoutId);
      img.onload = null;
      img.onerror = null;
    };
  }, [shouldLoad, src, fallback, onLoad, onError, hasError]);

  // Log do estado final antes do render (apenas quando necessário)
  const renderCountRef = useRef(0);
  renderCountRef.current++;

  if (renderCountRef.current === 1 || isLoaded || hasError) {
    console.log(`🎨 Renderizando LazyImage:`, {
      src: src.split('/').pop(),
      renderCount: renderCountRef.current,
      shouldLoad,
      isLoaded,
      hasError,
      loadTime: loadStartTime ? `${(performance.now() - loadStartTime).toFixed(2)}ms` : 'não iniciado',
      timestamp: new Date().toISOString()
    });
  }

  return (
    <div
      ref={imgRef}
      className={cn(
        'relative overflow-hidden bg-gray-100 dark:bg-gray-800',
        className
      )}
      style={{ width, height }}
      {...props}
    >
      {/* Loading state melhorado */}
      {!isLoaded && !hasError && (
        <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center">
          <div className="text-center">
            {/* Spinner animado */}
            <div className="w-10 h-10 border-3 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-3 mx-auto"></div>
            {/* Texto de loading */}
            <div className="text-sm text-gray-600 dark:text-gray-400 font-medium">
              Carregando...
            </div>
            {/* Barra de progresso animada */}
            <div className="w-20 h-1 bg-gray-200 dark:bg-gray-600 rounded-full mt-2 mx-auto overflow-hidden">
              <div className="h-full bg-blue-500 rounded-full animate-pulse w-3/4"></div>
            </div>
          </div>
        </div>
      )}

      {/* Imagem principal */}
      {imageSrc && (
        <img
          src={imageSrc}
          alt={alt}
          className={cn(
            'w-full h-full object-cover transition-opacity duration-300',
            isLoaded ? 'opacity-100' : 'opacity-0'
          )}
          loading={priority ? 'eager' : 'lazy'}
          decoding="async"
          onLoad={() => {
            setIsLoaded(true);
            onLoad?.();
          }}
          onError={() => {
            if (!hasError) {
              setHasError(true);
              setImageSrc(fallback);
            }
          }}
        />
      )}

      {/* Error state melhorado */}
      {hasError && imageSrc === fallback && (
        <div className="absolute inset-0 bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 flex items-center justify-center border-2 border-dashed border-red-200 dark:border-red-700">
          <div className="text-center text-red-600 dark:text-red-400">
            <div className="text-3xl mb-2">⚠️</div>
            <div className="text-sm font-medium">Erro ao carregar</div>
            <div className="text-xs mt-1 opacity-75">Verifique a conexão</div>
          </div>
        </div>
      )}
    </div>
  );
};

// Memoizar o componente para evitar re-renders desnecessários
export const LazyImage = React.memo(LazyImageComponent, (prevProps, nextProps) => {
  // Só re-renderizar se as props essenciais mudaram
  return (
    prevProps.src === nextProps.src &&
    prevProps.alt === nextProps.alt &&
    prevProps.className === nextProps.className &&
    prevProps.priority === nextProps.priority
  );
});

/**
 * Componente LazyImage simplificado para casos básicos
 */
export const SimpleLazyImage: React.FC<Omit<LazyImageProps, 'blur' | 'priority'>> = (props) => {
  return <LazyImage {...props} blur={false} priority={false} />;
};

/**
 * Componente LazyImage para imagens críticas (carregamento imediato)
 */
export const PriorityImage: React.FC<Omit<LazyImageProps, 'priority'>> = (props) => {
  return <LazyImage {...props} priority={true} />;
};
