import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  fallback?: string;
  blur?: boolean;
  priority?: boolean;
  onLoad?: () => void;
  onError?: () => void;
  width?: number;
  height?: number;
}

/**
 * Hook para Intersection Observer ultra-otimizado
 */
const useIntersectionObserver = (
  ref: React.RefObject<HTMLElement>,
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false);

  useEffect(() => {
    const element = ref.current;
    if (!element || isIntersecting) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsIntersecting(true);
          observer.unobserve(element);
        }
      },
      {
        rootMargin: '50px',
        threshold: 0.1,
        ...options
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [ref, isIntersecting]);

  return isIntersecting;
};



/**
 * Componente LazyImage otimizado com progressive loading
 */
const LazyImageComponent: React.FC<LazyImageProps> = ({
  src,
  alt,
  className,
  fallback = '/faviconx.webp',
  blur = true,
  priority = false,
  onLoad,
  onError,
  width,
  height,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [imageSrc, setImageSrc] = useState<string>('');

  const imgRef = useRef<HTMLDivElement>(null);

  // Logs simplificados apenas para debug crítico
  const hasLoggedRef = useRef(new Set<string>());

  useEffect(() => {
    if (!hasLoggedRef.current.has(src)) {
      hasLoggedRef.current.add(src);
      // Log apenas se for desenvolvimento
      if (process.env.NODE_ENV === 'development') {
        console.log(`🖼️ LazyImage: ${src.split('/').pop()}`);
      }
    }
  }, [src]);
  
  // Usar intersection observer apenas se não for priority
  const isInView = useIntersectionObserver(imgRef, {
    rootMargin: '100px'
  });

  const shouldLoad = priority || isInView;

  // Remover logs de estado para melhor performance

  // Remover logs de rede para melhor performance

  useEffect(() => {
    if (!shouldLoad || hasError) return;

    const img = new Image();

    img.onload = () => {
      setImageSrc(src);
      setIsLoaded(true);
      onLoad?.();
    };

    img.onerror = () => {
      setHasError(true);
      setImageSrc(fallback);
      onError?.();
    };

    img.src = src;

    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [shouldLoad, src, fallback, onLoad, onError, hasError]);

  // Remover logs de render para melhor performance

  return (
    <div
      ref={imgRef}
      className={cn(
        'relative overflow-hidden bg-gray-100 dark:bg-gray-800',
        className
      )}
      style={{ width, height }}
      {...props}
    >
      {/* Loading state melhorado */}
      {!isLoaded && !hasError && (
        <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center">
          <div className="text-center">
            {/* Spinner animado */}
            <div className="w-10 h-10 border-3 border-gray-300 border-t-blue-500 rounded-full animate-spin mb-3 mx-auto"></div>
            {/* Texto de loading */}
            <div className="text-sm text-gray-600 dark:text-gray-400 font-medium">
              Carregando...
            </div>
            {/* Barra de progresso animada */}
            <div className="w-20 h-1 bg-gray-200 dark:bg-gray-600 rounded-full mt-2 mx-auto overflow-hidden">
              <div className="h-full bg-blue-500 rounded-full animate-pulse w-3/4"></div>
            </div>
          </div>
        </div>
      )}

      {/* Imagem principal */}
      {imageSrc && (
        <img
          src={imageSrc}
          alt={alt}
          className={cn(
            'w-full h-full object-cover transition-opacity duration-300',
            isLoaded ? 'opacity-100' : 'opacity-0'
          )}
          loading={priority ? 'eager' : 'lazy'}
          decoding="async"
          onLoad={() => {
            setIsLoaded(true);
            onLoad?.();
          }}
          onError={() => {
            if (!hasError) {
              setHasError(true);
              setImageSrc(fallback);
            }
          }}
        />
      )}

      {/* Error state melhorado */}
      {hasError && imageSrc === fallback && (
        <div className="absolute inset-0 bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 flex items-center justify-center border-2 border-dashed border-red-200 dark:border-red-700">
          <div className="text-center text-red-600 dark:text-red-400">
            <div className="text-3xl mb-2">⚠️</div>
            <div className="text-sm font-medium">Erro ao carregar</div>
            <div className="text-xs mt-1 opacity-75">Verifique a conexão</div>
          </div>
        </div>
      )}
    </div>
  );
};

// Memoizar o componente de forma simples e eficiente
export const LazyImage = React.memo(LazyImageComponent);

/**
 * Componente LazyImage simplificado para casos básicos
 */
export const SimpleLazyImage: React.FC<Omit<LazyImageProps, 'blur' | 'priority'>> = (props) => {
  return <LazyImage {...props} blur={false} priority={false} />;
};

/**
 * Componente LazyImage para imagens críticas (carregamento imediato)
 */
export const PriorityImage: React.FC<Omit<LazyImageProps, 'priority'>> = (props) => {
  return <LazyImage {...props} priority={true} />;
};
