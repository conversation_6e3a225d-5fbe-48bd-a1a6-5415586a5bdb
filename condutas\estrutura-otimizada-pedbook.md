# Estrutura Otimizada PedBook - Condutas e Manejos

## Visão Geral da Nova Arquitetura

### Objetivos
- **Clean e fácil manutenção**: Estrutura simples e intuitiva
- **Separação clara**: Condutas/Manejos vs Tratamento
- **Responsivo**: Desktop (menu lateral) e Mobile (FAQ)
- **Escalável**: Suporte para crescimento do conteúdo

### Estrutura de Dados

#### Tabela: `pedbook_conducts_optimized`

```sql
CREATE TABLE pedbook_conducts_optimized (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  topic_id UUID REFERENCES pedbook_conducts_topics(id),
  title TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  
  -- Separação clara entre Condutas/Manejos e Tratamento
  conducts_content JSONB NOT NULL DEFAULT '{}',
  treatment_content JSONB NOT NULL DEFAULT '{}',
  
  -- Estrutura para menu lateral/FAQ
  navigation_sections JSONB DEFAULT '[]',
  
  -- Metadados otimizados
  format_type TEXT DEFAULT 'optimized',
  content_version INTEGER DEFAULT 1,
  
  -- Flags de controle
  published BOOLEAN DEFAULT false,
  has_treatment BOOLEAN DEFAULT true,
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Estrutura do Conteúdo JSONB

### 1. `conducts_content` - Condutas e Manejos

```json
{
  "sections": [
    {
      "id": "definicao",
      "title": "Definição e Contexto Clínico",
      "content": "Conteúdo em markdown...",
      "order": 1,
      "show_in_menu": true
    },
    {
      "id": "classificacao", 
      "title": "Classificação",
      "content": "Conteúdo com tabelas...",
      "order": 2,
      "show_in_menu": true,
      "subsections": [
        {
          "id": "por-tempo",
          "title": "Por Tempo de Aparecimento",
          "content": "..."
        }
      ]
    }
  ],
  "metadata": {
    "last_updated": "2025-01-XX",
    "version": "1.0",
    "author": "Sistema PedBook"
  }
}
```

### 2. `treatment_content` - Tratamento

```json
{
  "sections": [
    {
      "id": "farmacologico",
      "title": "Tratamento Farmacológico", 
      "content": "Medicações e dosagens...",
      "order": 1,
      "show_in_menu": true
    },
    {
      "id": "nao-farmacologico",
      "title": "Tratamento Não-Farmacológico",
      "content": "Medidas gerais...",
      "order": 2,
      "show_in_menu": true
    },
    {
      "id": "seguimento",
      "title": "Seguimento e Monitorização",
      "content": "Acompanhamento...",
      "order": 3,
      "show_in_menu": true
    }
  ],
  "prescriptions": [
    {
      "medication": "Paracetamol",
      "dosage": "10-15mg/kg/dose",
      "frequency": "6/6h",
      "route": "VO",
      "notes": "Máximo 5 doses/dia"
    }
  ]
}
```

### 3. `navigation_sections` - Menu/FAQ

```json
[
  {
    "id": "definicao",
    "title": "O que é?",
    "target_section": "conducts_content.sections[0]",
    "order": 1
  },
  {
    "id": "como-tratar",
    "title": "Como tratar?",
    "target_section": "treatment_content.sections[0]",
    "order": 2
  },
  {
    "id": "quando-encaminhar",
    "title": "Quando encaminhar?",
    "target_section": "conducts_content.sections[5]",
    "order": 3
  }
]
```

## Padrão de Formatação Otimizada

### Markdown Estruturado

```markdown
# Título Principal

## Seção Principal
Conteúdo da seção...

### Subseção
Conteúdo da subseção...

<!-- TABELA_INICIO:nome_tabela -->
| Coluna 1 | Coluna 2 | Coluna 3 |
|----------|----------|----------|
| Valor 1  | Valor 2  | Valor 3  |
<!-- TABELA_FIM:nome_tabela -->

**Destaque importante:** Texto em negrito
*Ênfase:* Texto em itálico

### Lista de Verificação
- [ ] Item não marcado
- [x] Item marcado
- [ ] Outro item

### Alertas
> **⚠️ ATENÇÃO:** Informação crítica
> **ℹ️ NOTA:** Informação adicional
> **🚨 EMERGÊNCIA:** Situação de urgência
```

## Interface do Usuário

### Desktop
- **Menu lateral fixo** com navegação por seções
- **Duas abas principais**: "Condutas & Manejos" | "Tratamento"
- **Busca rápida** dentro do conteúdo
- **Índice flutuante** para navegação rápida

### Mobile  
- **FAQ expansível** baseado nas seções
- **Navegação por abas** deslizantes
- **Busca otimizada** para tela pequena
- **Conteúdo responsivo** com tabelas scrolláveis

## Benefícios da Nova Estrutura

### Para Desenvolvedores
- **Manutenção simplificada**: Estrutura JSONB flexível
- **Versionamento**: Controle de versões do conteúdo
- **Escalabilidade**: Fácil adição de novos campos
- **Performance**: Índices otimizados

### Para Usuários
- **Navegação intuitiva**: Menu lateral/FAQ claro
- **Conteúdo organizado**: Separação lógica
- **Busca eficiente**: Encontrar informações rapidamente
- **Experiência consistente**: Desktop e mobile

### Para Editores de Conteúdo
- **Estrutura clara**: Seções bem definidas
- **Flexibilidade**: Adicionar/remover seções facilmente
- **Controle de qualidade**: Validação de estrutura
- **Workflow otimizado**: Edição por seções

## Migração dos Dados Existentes

### Etapas
1. **Análise do conteúdo atual** em `pedbook_conducts_summaries`
2. **Separação automática** entre condutas e tratamento
3. **Conversão para nova estrutura** JSONB
4. **Validação e testes** com conteúdo migrado
5. **Deploy gradual** com fallback para estrutura antiga

### Script de Migração
```sql
-- Exemplo de migração
INSERT INTO pedbook_conducts_optimized (
  topic_id, title, slug, conducts_content, treatment_content
)
SELECT 
  topic_id,
  title,
  slug,
  -- Lógica para separar conteúdo
  extract_conducts_content(content),
  extract_treatment_content(content)
FROM pedbook_conducts_summaries
WHERE published = true;
```
