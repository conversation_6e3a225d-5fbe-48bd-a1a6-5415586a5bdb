#!/usr/bin/env python3
"""
Script para conversão do formato atual para o formato otimizado PedBook
Converte conteúdo HTML da tabela pedbook_conducts_summaries para a nova estrutura JSONB
"""

import json
import re
from typing import Dict, List, Any
from bs4 import BeautifulSoup
import html2text

class PedBookContentConverter:
    def __init__(self):
        self.h2t = html2text.HTML2Text()
        self.h2t.ignore_links = False
        self.h2t.ignore_images = False
        self.h2t.body_width = 0  # Não quebrar linhas
        
    def convert_html_to_markdown(self, html_content: str) -> str:
        """Converte HTML para Markdown limpo"""
        if not html_content:
            return ""
            
        # Limpar HTML problemático
        html_content = self.clean_html(html_content)
        
        # Converter para markdown
        markdown = self.h2t.handle(html_content)
        
        # Limpar markdown resultante
        markdown = self.clean_markdown(markdown)
        
        return markdown.strip()
    
    def clean_html(self, html: str) -> str:
        """Limpa e padroniza HTML antes da conversão"""
        # Remover tags problemáticas
        html = re.sub(r'</?span[^>]*>', '', html)
        html = re.sub(r'</?div[^>]*>', '', html)
        
        # Padronizar títulos com ##
        html = re.sub(r'<h2><strong>##\.?\s*([^<]+)</strong></h2>', r'<h2>\1</h2>', html)
        html = re.sub(r'<h2>##\.?\s*([^<]+)</h2>', r'<h2>\1</h2>', html)
        
        return html
    
    def clean_markdown(self, markdown: str) -> str:
        """Limpa e padroniza markdown resultante"""
        # Remover linhas vazias excessivas
        markdown = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown)
        
        # Padronizar títulos
        markdown = re.sub(r'^##\s*(.+)$', r'## \1', markdown, flags=re.MULTILINE)
        
        # Limpar formatação excessiva
        markdown = re.sub(r'\*\*\*\*([^*]+)\*\*\*\*', r'**\1**', markdown)
        
        return markdown
    
    def extract_sections(self, markdown_content: str) -> List[Dict[str, Any]]:
        """Extrai seções do conteúdo markdown"""
        sections = []
        
        # Dividir por títulos H2
        h2_pattern = r'^## (.+)$'
        parts = re.split(h2_pattern, markdown_content, flags=re.MULTILINE)
        
        if len(parts) > 1:
            # Primeiro item é conteúdo antes do primeiro H2 (geralmente vazio)
            for i in range(1, len(parts), 2):
                if i + 1 < len(parts):
                    title = parts[i].strip()
                    content = parts[i + 1].strip()
                    
                    if title and content:
                        section = {
                            "id": self.generate_section_id(title),
                            "title": title,
                            "content": content,
                            "order": len(sections) + 1,
                            "show_in_menu": True
                        }
                        
                        # Extrair subseções se houver
                        subsections = self.extract_subsections(content)
                        if subsections:
                            section["subsections"] = subsections
                        
                        sections.append(section)
        
        return sections
    
    def extract_subsections(self, content: str) -> List[Dict[str, Any]]:
        """Extrai subseções (H3) do conteúdo"""
        subsections = []
        
        h3_pattern = r'^### (.+)$'
        parts = re.split(h3_pattern, content, flags=re.MULTILINE)
        
        if len(parts) > 1:
            for i in range(1, len(parts), 2):
                if i + 1 < len(parts):
                    title = parts[i].strip()
                    subcontent = parts[i + 1].strip()
                    
                    if title and subcontent:
                        subsections.append({
                            "id": self.generate_section_id(title),
                            "title": title,
                            "content": subcontent
                        })
        
        return subsections
    
    def generate_section_id(self, title: str) -> str:
        """Gera ID único para seção baseado no título"""
        # Remover acentos e caracteres especiais
        id_str = title.lower()
        id_str = re.sub(r'[àáâãäå]', 'a', id_str)
        id_str = re.sub(r'[èéêë]', 'e', id_str)
        id_str = re.sub(r'[ìíîï]', 'i', id_str)
        id_str = re.sub(r'[òóôõö]', 'o', id_str)
        id_str = re.sub(r'[ùúûü]', 'u', id_str)
        id_str = re.sub(r'[ç]', 'c', id_str)
        id_str = re.sub(r'[ñ]', 'n', id_str)
        
        # Manter apenas letras, números e hífens
        id_str = re.sub(r'[^a-z0-9\s-]', '', id_str)
        id_str = re.sub(r'\s+', '-', id_str)
        id_str = re.sub(r'-+', '-', id_str)
        id_str = id_str.strip('-')
        
        return id_str
    
    def separate_conducts_and_treatment(self, sections: List[Dict]) -> tuple:
        """Separa seções entre condutas/manejos e tratamento"""
        treatment_keywords = [
            'tratamento', 'terapia', 'medicação', 'fármaco', 'droga',
            'prescrição', 'dose', 'posologia', 'administração'
        ]
        
        conducts_sections = []
        treatment_sections = []
        
        for section in sections:
            title_lower = section['title'].lower()
            content_lower = section['content'].lower()
            
            # Verificar se é seção de tratamento
            is_treatment = any(keyword in title_lower for keyword in treatment_keywords)
            
            # Verificar também no conteúdo se não foi identificado pelo título
            if not is_treatment:
                treatment_content_ratio = sum(
                    content_lower.count(keyword) for keyword in treatment_keywords
                ) / max(len(content_lower.split()), 1)
                
                is_treatment = treatment_content_ratio > 0.02  # 2% do conteúdo
            
            if is_treatment:
                treatment_sections.append(section)
            else:
                conducts_sections.append(section)
        
        return conducts_sections, treatment_sections
    
    def generate_navigation_sections(self, conducts_sections: List[Dict], 
                                   treatment_sections: List[Dict]) -> List[Dict]:
        """Gera seções de navegação para menu lateral/FAQ"""
        navigation = []
        order = 1
        
        # Mapear seções de condutas para perguntas FAQ
        faq_mapping = {
            'definicao': 'O que é?',
            'classificacao': 'Quais os tipos?',
            'fatores-risco': 'Quais os fatores de risco?',
            'quadro-clinico': 'Como se apresenta?',
            'diagnostico': 'Como diagnosticar?',
            'avaliacao': 'Como avaliar?',
            'investigacao': 'Quais exames solicitar?',
            'criterios': 'Quando tratar?'
        }
        
        # Adicionar navegação para condutas
        for section in conducts_sections:
            section_id = section['id']
            title = faq_mapping.get(section_id, f"Sobre {section['title']}")
            
            navigation.append({
                "id": section_id,
                "title": title,
                "target_section": f"conducts_content.sections[{len(navigation)}]",
                "order": order,
                "icon": self.get_section_icon(section_id)
            })
            order += 1
        
        # Adicionar navegação para tratamento
        if treatment_sections:
            navigation.append({
                "id": "como-tratar",
                "title": "Como tratar?",
                "target_section": "treatment_content.sections[0]",
                "order": order,
                "icon": "treatment"
            })
            order += 1
            
            if len(treatment_sections) > 1:
                navigation.append({
                    "id": "seguimento",
                    "title": "Como fazer seguimento?",
                    "target_section": f"treatment_content.sections[{len(treatment_sections)-1}]",
                    "order": order,
                    "icon": "follow-up"
                })
        
        return navigation
    
    def get_section_icon(self, section_id: str) -> str:
        """Retorna ícone apropriado para a seção"""
        icon_mapping = {
            'definicao': 'info',
            'classificacao': 'list',
            'fatores-risco': 'warning',
            'quadro-clinico': 'symptoms',
            'diagnostico': 'diagnosis',
            'avaliacao': 'search',
            'investigacao': 'lab',
            'criterios': 'decision'
        }
        return icon_mapping.get(section_id, 'info')
    
    def convert_content(self, html_content: str, title: str) -> Dict[str, Any]:
        """Converte conteúdo completo para novo formato"""
        # Converter HTML para Markdown
        markdown_content = self.convert_html_to_markdown(html_content)
        
        # Extrair seções
        all_sections = self.extract_sections(markdown_content)
        
        # Separar condutas e tratamento
        conducts_sections, treatment_sections = self.separate_conducts_and_treatment(all_sections)
        
        # Gerar navegação
        navigation_sections = self.generate_navigation_sections(conducts_sections, treatment_sections)
        
        # Estruturar resultado
        result = {
            "conducts_content": {
                "sections": conducts_sections,
                "metadata": {
                    "last_updated": "2025-01-15",
                    "version": "1.0",
                    "author": "Sistema PedBook Converter",
                    "conversion_source": "pedbook_conducts_summaries"
                }
            },
            "treatment_content": {
                "sections": treatment_sections,
                "prescriptions": [],  # A ser preenchido manualmente
                "complications": []   # A ser preenchido manualmente
            },
            "navigation_sections": navigation_sections,
            "format_type": "optimized",
            "has_treatment": len(treatment_sections) > 0
        }
        
        return result

# Exemplo de uso
if __name__ == "__main__":
    converter = PedBookContentConverter()
    
    # Exemplo com conteúdo HTML
    sample_html = """
    <h2><strong>## Definição</strong></h2>
    <p>O <strong>Alojamento Conjunto</strong> é um sistema...</p>
    <h2><strong>## Principais Benefícios</strong></h2>
    <ul><li><strong>Humanização do Atendimento:</strong> Facilita...</li></ul>
    """
    
    result = converter.convert_content(sample_html, "Alojamento Conjunto")
    print(json.dumps(result, indent=2, ensure_ascii=False))
