import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { ImagePlus, Loader2, Upload } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface ImageUploadModalProps {
  onImageInserted: (markdownText: string) => void;
  children?: React.ReactNode;
}

export const ImageUploadModal: React.FC<ImageUploadModalProps> = ({
  onImageInserted,
  children
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>("");
  const [title, setTitle] = useState("");
  const [source, setSource] = useState("");
  const [isGlobal, setIsGlobal] = useState(false);
  const { toast } = useToast();

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImageFile(file);
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);
    }
  };

  const handleUpload = async () => {
    if (!imageFile) {
      toast({
        title: "Erro",
        description: "Selecione uma imagem primeiro",
        variant: "destructive",
      });
      return;
    }

    try {
      setUploading(true);

      // Converter para WebP e fazer upload
      const objectUrl = URL.createObjectURL(imageFile);
      const imgElement = document.createElement('img');
      await new Promise((resolve, reject) => {
        imgElement.onload = resolve;
        imgElement.onerror = reject;
        imgElement.src = objectUrl;
      });

      const canvas = document.createElement('canvas');
      canvas.width = imgElement.width;
      canvas.height = imgElement.height;
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        throw new Error("Could not get canvas context");
      }

      ctx.drawImage(imgElement, 0, 0);
      URL.revokeObjectURL(objectUrl);

      const webpBlob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => resolve(blob!), 'image/webp', 0.8);
      });

      const fileName = `${crypto.randomUUID()}.webp`;
      const { data, error: uploadError } = await supabase.storage
        .from('category-icons')
        .upload(fileName, webpBlob);

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('category-icons')
        .getPublicUrl(fileName);

      // Criar markdown customizado
      let markdownText = `<ImageWithCaption src="${publicUrl}"`;

      if (title.trim()) {
        markdownText += ` title="${title.trim()}"`;
      }

      if (source.trim()) {
        markdownText += ` source="${source.trim()}"`;
      }

      if (isGlobal) {
        markdownText += ` global="true"`;
      }

      markdownText += ` />`;

      // Inserir no editor
      onImageInserted(markdownText);

      // Limpar formulário e fechar modal
      setImageFile(null);
      setImagePreview("");
      setTitle("");
      setSource("");
      setIsGlobal(false);
      setIsOpen(false);

      toast({ 
        title: "Sucesso", 
        description: "Imagem inserida com sucesso!" 
      });

    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: "Erro ao enviar imagem",
        description: "Ocorreu um erro ao fazer upload da imagem",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const resetForm = () => {
    setImageFile(null);
    setImagePreview("");
    setTitle("");
    setSource("");
    setIsGlobal(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      setIsOpen(open);
      if (!open) resetForm();
    }}>
      <DialogTrigger asChild>
        {children || (
          <Button variant="outline" size="sm">
            <ImagePlus className="w-4 h-4 mr-2" />
            Adicionar Imagem
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Adicionar Imagem</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Upload de arquivo */}
          <div>
            <Label htmlFor="image-file">Imagem *</Label>
            <Input
              id="image-file"
              type="file"
              accept="image/*"
              onChange={handleFileSelect}
              className="mt-1"
            />
          </div>

          {/* Preview da imagem */}
          {imagePreview && (
            <div className="text-center">
              <img
                src={imagePreview}
                alt="Preview"
                className="max-w-full h-32 object-cover rounded-md mx-auto"
              />
            </div>
          )}

          {/* Título da imagem */}
          <div>
            <Label htmlFor="image-title">Título da Imagem</Label>
            <Input
              id="image-title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Ex: Lesão descamativa com bolhas flácidas na mão do RN"
              className="mt-1"
            />
          </div>

          {/* Fonte da imagem */}
          <div>
            <Label htmlFor="image-source">Fonte</Label>
            <Textarea
              id="image-source"
              value={source}
              onChange={(e) => setSource(e.target.value)}
              placeholder="Ex: CNX OpenStax, licenciada sob domínio CC BY-SA 4.0"
              className="mt-1 h-20"
            />
          </div>

          {/* Checkbox para imagem global */}
          <div className="flex items-center space-x-2 p-4 bg-purple-50 border border-purple-200 rounded-lg">
            <Checkbox
              id="global-image"
              checked={isGlobal}
              onCheckedChange={(checked) => setIsGlobal(checked as boolean)}
            />
            <div className="grid gap-1.5 leading-none">
              <Label
                htmlFor="global-image"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                🖼️ Imagem Global
              </Label>
              <p className="text-xs text-purple-600">
                Marque para que esta imagem apareça na galeria de imagens do resumo
              </p>
            </div>
          </div>

          {/* Botões */}
          <div className="flex gap-2 pt-4">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="flex-1"
            >
              Cancelar
            </Button>
            <Button
              onClick={handleUpload}
              disabled={!imageFile || uploading}
              className="flex-1"
            >
              {uploading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Enviando...
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4 mr-2" />
                  Inserir
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
