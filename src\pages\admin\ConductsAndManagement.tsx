
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, Edit, Trash } from "lucide-react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { CategoryDialog } from "@/components/admin/conducts/CategoryDialog";
import { TopicDialog } from "@/components/admin/conducts/TopicDialog";
import { SubcategoryDialog } from "@/components/admin/conducts/SubcategoryDialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface Category {
  id: string;
  name: string;
  description: string | null;
  icon: string | null;
  color: string | null;
  slug: string;
  display_order: number | null;
}

interface Topic {
  id: string;
  name: string;
  description: string | null;
  icon: string | null;
  slug: string;
  category_id: string;
  parent_id?: string | null;
  is_subcategory?: boolean | null;
  display_order: number | null;
  published?: boolean;
  parent_topic?: {
    id: string;
    name: string;
    is_subcategory: boolean;
  } | null;
  child_topics?: {
    id: string;
    name: string;
    is_subcategory: boolean;
    display_order: number;
  }[];
}

const ConductsAndManagement = () => {
  const [isCategoryDialogOpen, setIsCategoryDialogOpen] = useState(false);
  const [isTopicDialogOpen, setIsTopicDialogOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | undefined>();
  const [selectedTopic, setSelectedTopic] = useState<Topic | undefined>();
  const [categoryToDelete, setCategoryToDelete] = useState<Category | undefined>();
  const [topicToDelete, setTopicToDelete] = useState<Topic | undefined>();
  const [selectedCategoryForTopic, setSelectedCategoryForTopic] = useState<string>("");
  const [isSubcategoryDialogOpen, setIsSubcategoryDialogOpen] = useState(false);
  const [selectedSubcategory, setSelectedSubcategory] = useState<Topic | undefined>();
  const [selectedCategoryForSubcategory, setSelectedCategoryForSubcategory] = useState<string>("");
  const queryClient = useQueryClient();

  const { data: categories, isLoading: isLoadingCategories } = useQuery({
    queryKey: ['conducts-categories'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_conducts_categories')
        .select('*')
        .order('display_order', { ascending: true, nullsFirst: true });

      if (error) throw error;
      return data as Category[];
    }
  });

  const { data: topics, isLoading: isLoadingTopics } = useQuery({
    queryKey: ['conducts-topics'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('pedbook_conducts_topics')
        .select(`
          *,
          parent_topic:parent_id(id, name, is_subcategory),
          child_topics:pedbook_conducts_topics!parent_id(id, name, is_subcategory, display_order)
        `)
        .order('display_order', { ascending: true, nullsFirst: true });

      if (error) throw error;
      return data as Topic[];
    }
  });

  const handleEditCategory = (category: Category) => {
    setSelectedCategory(category);
    setIsCategoryDialogOpen(true);
  };

  const handleEditTopic = (topic: Topic) => {
    console.log('🔄 Editando tópico:', topic.id, topic.name);
    setSelectedTopic(topic);
    setSelectedCategoryForTopic(topic.category_id);
    setIsTopicDialogOpen(true);
  };

  const handleDeleteCategory = async (category: Category) => {
    try {
      console.log('Iniciando processo de exclusão da categoria:', category.id);

      // Primeiro, buscar todos os tópicos da categoria
      const { data: topics, error: fetchError } = await supabase
        .from('pedbook_conducts_topics')
        .select('id')
        .eq('category_id', category.id);

      if (fetchError) {
        console.error('Erro ao buscar tópicos:', fetchError);
        throw fetchError;
      }

      console.log('Tópicos encontrados:', topics);

      // Se existem tópicos, deletar todos eles primeiro
      if (topics && topics.length > 0) {
        const topicIds = topics.map(topic => topic.id);

        // 1. Deletar da tabela otimizada primeiro
        const { error: optimizedError } = await supabase
          .from('pedbook_conducts_optimized')
          .delete()
          .in('topic_id', topicIds);

        if (optimizedError) {
          console.error('Erro ao deletar conteúdo otimizado:', optimizedError);
          throw optimizedError;
        }

        // 2. Deletar os summaries dos tópicos
        const { error: summaryError } = await supabase
          .from('pedbook_conducts_summaries')
          .delete()
          .in('topic_id', topicIds);

        if (summaryError) {
          console.error('Erro ao deletar summaries:', summaryError);
          throw summaryError;
        }

        // 3. Deletar o conteúdo dos tópicos
        const { error: contentError } = await supabase
          .from('pedbook_conducts_content')
          .delete()
          .in('topic_id', topicIds);

        if (contentError) {
          console.error('Erro ao deletar conteúdo:', contentError);
          throw contentError;
        }

        // 4. Deletar o feedback dos tópicos
        const { error: feedbackError } = await supabase
          .from('pedbook_conducts_feedback')
          .delete()
          .in('summary_id', topicIds);

        if (feedbackError) {
          console.error('Erro ao deletar feedback:', feedbackError);
          throw feedbackError;
        }

        // 5. Deletar os tópicos
        const { error: topicsError } = await supabase
          .from('pedbook_conducts_topics')
          .delete()
          .eq('category_id', category.id);

        if (topicsError) {
          console.error('Erro ao deletar tópicos:', topicsError);
          throw topicsError;
        }
      }

      // Finalmente, deletar a categoria
      const { error: categoryError } = await supabase
        .from('pedbook_conducts_categories')
        .delete()
        .eq('id', category.id);

      if (categoryError) throw categoryError;

      toast({ title: "Categoria removida com sucesso" });
      queryClient.invalidateQueries({ queryKey: ['conducts-categories'] });
      setCategoryToDelete(undefined);
    } catch (error: any) {
      console.error('Error:', error);
      toast({
        title: "Erro ao remover categoria",
        description: error.message || "Ocorreu um erro ao remover a categoria",
        variant: "destructive",
      });
    }
  };

  const handleDeleteTopic = async (topic: Topic) => {
    try {
      console.log('🗑️ Iniciando processo de deleção do tópico:', topic.id);

      // 1. Primeiro, deletar da tabela otimizada (se existir)
      const { error: optimizedError } = await supabase
        .from('pedbook_conducts_optimized')
        .delete()
        .eq('topic_id', topic.id);

      if (optimizedError) {
        console.error('❌ Erro ao deletar conteúdo otimizado:', optimizedError);
        throw optimizedError;
      }

      console.log('✅ Conteúdo otimizado deletado com sucesso');

      // 2. Deletar os resumos associados
      const { error: summaryError } = await supabase
        .from('pedbook_conducts_summaries')
        .delete()
        .eq('topic_id', topic.id);

      if (summaryError) {
        console.error('❌ Erro ao deletar resumos:', summaryError);
        throw summaryError;
      }

      console.log('✅ Resumos deletados com sucesso');

      // 3. Deletar conteúdo adicional (se existir)
      const { error: contentError } = await supabase
        .from('pedbook_conducts_content')
        .delete()
        .eq('topic_id', topic.id);

      if (contentError) {
        console.error('❌ Erro ao deletar conteúdo:', contentError);
        throw contentError;
      }

      console.log('✅ Conteúdo adicional deletado com sucesso');

      // 4. Deletar feedback associado (se existir)
      const { error: feedbackError } = await supabase
        .from('pedbook_conducts_feedback')
        .delete()
        .eq('summary_id', topic.id);

      if (feedbackError) {
        console.error('❌ Erro ao deletar feedback:', feedbackError);
        throw feedbackError;
      }

      console.log('✅ Feedback deletado com sucesso');

      // 5. Finalmente, deletar o tópico
      const { error: topicError } = await supabase
        .from('pedbook_conducts_topics')
        .delete()
        .eq('id', topic.id);

      if (topicError) {
        console.error('❌ Erro ao deletar tópico:', topicError);
        throw topicError;
      }

      console.log('✅ Tópico deletado com sucesso');
      toast({ title: "Tópico removido com sucesso" });
      setTopicToDelete(undefined);
      queryClient.invalidateQueries({ queryKey: ['conducts-topics'] });

    } catch (error: any) {
      console.error('❌ Error:', error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao remover o tópico",
        variant: "destructive",
      });
    }
  };

  const handleAddNewTopic = (categoryId: string, parentId?: string) => {
    console.log('🆕 Criando novo tópico para categoria:', categoryId, 'parent:', parentId);

    if (parentId) {
      // Criar tópico filho de subcategoria
      setSelectedTopic({
        id: '',
        name: '',
        description: null,
        icon: null,
        slug: '',
        category_id: categoryId,
        parent_id: parentId,
        is_subcategory: false,
        display_order: null
      } as Topic);
    } else {
      setSelectedTopic(undefined);
    }

    setSelectedCategoryForTopic(categoryId);
    setIsTopicDialogOpen(true);
  };

  const handleAddNewSubcategory = (categoryId: string) => {
    console.log('🆕 Criando nova subcategoria para categoria:', categoryId);
    setSelectedSubcategory(undefined);
    setSelectedCategoryForSubcategory(categoryId);
    setIsSubcategoryDialogOpen(true);
  };

  const handleEditSubcategory = (subcategory: Topic) => {
    console.log('🔄 Editando subcategoria:', subcategory.id, subcategory.name);
    setSelectedSubcategory(subcategory);
    setSelectedCategoryForSubcategory(subcategory.category_id);
    setIsSubcategoryDialogOpen(true);
  };

  // Organizar tópicos em estrutura hierárquica
  const organizeTopicsHierarchy = (topics: Topic[], categoryId: string) => {
    if (!topics) return [];

    const categoryTopics = topics.filter(topic => topic.category_id === categoryId);

    // Separar subcategorias e tópicos normais
    const subcategories = categoryTopics.filter(topic => topic.is_subcategory && !topic.parent_id);
    const directTopics = categoryTopics.filter(topic => !topic.is_subcategory && !topic.parent_id);

    // Para cada subcategoria, buscar seus tópicos filhos
    const hierarchicalStructure = [
      ...subcategories.map(subcategory => ({
        ...subcategory,
        children: categoryTopics.filter(topic => topic.parent_id === subcategory.id)
      })),
      ...directTopics
    ];

    return hierarchicalStructure.sort((a, b) => (a.display_order || 0) - (b.display_order || 0));
  };

  if (isLoadingCategories || isLoadingTopics) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-slate-900">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary dark:border-blue-400"></div>
      </div>
    );
  }

  return (
    <div className="container py-8 bg-gray-50 dark:bg-slate-900">
      <Card className="dark:bg-slate-800 dark:border-slate-700">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="dark:text-white">Manejos e Condutas</CardTitle>
              <CardDescription className="dark:text-gray-300">
                Gerencie categorias e conteúdo de manejos e condutas
              </CardDescription>
            </div>
            <Button onClick={() => {
              setSelectedCategory(undefined);
              setIsCategoryDialogOpen(true);
            }}>
              <Plus className="w-4 h-4 mr-2" />
              Nova Categoria
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            {categories?.map((category) => (
              <Card key={category.id} className="dark:bg-slate-800/70 dark:border-slate-700">
                <CardContent className="p-4">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="font-semibold dark:text-white">{category.name}</h3>
                        {category.description && (
                          <p className="text-sm text-muted-foreground dark:text-gray-400">
                            {category.description}
                          </p>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="icon"
                          onClick={() => handleEditCategory(category)}
                          className="dark:border-slate-600 dark:hover:bg-slate-700"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="icon"
                          onClick={() => setCategoryToDelete(category)}
                          className="dark:border-slate-600 dark:hover:bg-slate-700"
                        >
                          <Trash className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Tópicos e Subcategorias */}
                    <div className="pl-4 space-y-2">
                      <div className="flex justify-between items-center">
                        <h4 className="text-sm font-medium dark:text-gray-200">Conteúdo</h4>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleAddNewSubcategory(category.id)}
                            className="dark:hover:bg-slate-700"
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            Nova Subcategoria
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleAddNewTopic(category.id)}
                            className="dark:hover:bg-slate-700"
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            Novo Tópico
                          </Button>
                        </div>
                      </div>
                      <div className="space-y-2">
                        {organizeTopicsHierarchy(topics || [], category.id).map(item => (
                          <div key={item.id} className="space-y-2">
                            {/* Item principal (subcategoria ou tópico) */}
                            <div className={`flex justify-between items-center p-2 rounded-lg border ${
                              item.is_subcategory
                                ? 'dark:border-blue-600 dark:bg-blue-900/20 border-blue-200 bg-blue-50'
                                : 'dark:border-slate-700 dark:bg-slate-800'
                            }`}>
                              <div className="flex items-center gap-2">
                                {item.is_subcategory && (
                                  <span className="text-blue-500 text-sm">📁</span>
                                )}
                                <div>
                                  <p className="font-medium dark:text-white">{item.name}</p>
                                  {item.description && (
                                    <p className="text-sm text-muted-foreground dark:text-gray-400">
                                      {item.description}
                                    </p>
                                  )}
                                  {item.is_subcategory && (
                                    <span className="text-xs text-blue-600 dark:text-blue-400">Subcategoria</span>
                                  )}
                                </div>
                              </div>
                              <div className="flex gap-2">
                                {item.is_subcategory && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleAddNewTopic(category.id, item.id)}
                                    className="dark:hover:bg-slate-700"
                                  >
                                    <Plus className="w-3 h-3 mr-1" />
                                    Tópico
                                  </Button>
                                )}
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => item.is_subcategory ? handleEditSubcategory(item) : handleEditTopic(item)}
                                  className="dark:hover:bg-slate-700"
                                >
                                  <Edit className="w-4 h-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => setTopicToDelete(item)}
                                  className="dark:hover:bg-slate-700"
                                >
                                  <Trash className="w-4 h-4" />
                                </Button>
                              </div>
                            </div>

                            {/* Tópicos filhos da subcategoria */}
                            {item.is_subcategory && (item as any).children?.length > 0 && (
                              <div className="ml-6 space-y-2">
                                {(item as any).children.map((child: Topic) => (
                                  <div
                                    key={child.id}
                                    className="flex justify-between items-center p-2 rounded-lg border dark:border-slate-700 dark:bg-slate-800/50"
                                  >
                                    <div className="flex items-center gap-2">
                                      <span className="text-gray-400 text-sm">└─</span>
                                      <div>
                                        <p className="font-medium dark:text-white">{child.name}</p>
                                        {child.description && (
                                          <p className="text-sm text-muted-foreground dark:text-gray-400">
                                            {child.description}
                                          </p>
                                        )}
                                      </div>
                                    </div>
                                    <div className="flex gap-2">
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => handleEditTopic(child)}
                                        className="dark:hover:bg-slate-700"
                                      >
                                        <Edit className="w-4 h-4" />
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => setTopicToDelete(child)}
                                        className="dark:hover:bg-slate-700"
                                      >
                                        <Trash className="w-4 h-4" />
                                      </Button>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      <CategoryDialog 
        category={selectedCategory}
        open={isCategoryDialogOpen}
        onOpenChange={setIsCategoryDialogOpen}
      />

      <TopicDialog
        topic={selectedTopic}
        categoryId={selectedCategoryForTopic}
        open={isTopicDialogOpen}
        onOpenChange={setIsTopicDialogOpen}
      />

      <SubcategoryDialog
        subcategory={selectedSubcategory}
        categoryId={selectedCategoryForSubcategory}
        open={isSubcategoryDialogOpen}
        onOpenChange={setIsSubcategoryDialogOpen}
      />

      <AlertDialog open={!!categoryToDelete} onOpenChange={() => setCategoryToDelete(undefined)}>
        <AlertDialogContent className="dark:bg-slate-800 dark:border-slate-700">
          <AlertDialogHeader>
            <AlertDialogTitle className="dark:text-white">Você tem certeza?</AlertDialogTitle>
            <AlertDialogDescription className="dark:text-gray-300">
              Esta ação não pode ser desfeita. Isso excluirá permanentemente a categoria e todos os seus tópicos.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="dark:bg-slate-700 dark:text-white dark:hover:bg-slate-600">Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={() => categoryToDelete && handleDeleteCategory(categoryToDelete)}>
              Continuar
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={!!topicToDelete} onOpenChange={() => setTopicToDelete(undefined)}>
        <AlertDialogContent className="dark:bg-slate-800 dark:border-slate-700">
          <AlertDialogHeader>
            <AlertDialogTitle className="dark:text-white">Você tem certeza?</AlertDialogTitle>
            <AlertDialogDescription className="dark:text-gray-300">
              Esta ação não pode ser desfeita. Isso excluirá permanentemente o tópico e todo seu conteúdo.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="dark:bg-slate-700 dark:text-white dark:hover:bg-slate-600">Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={() => topicToDelete && handleDeleteTopic(topicToDelete)}>
              Continuar
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ConductsAndManagement;
