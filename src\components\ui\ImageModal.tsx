import React, { useEffect } from 'react';
import { Dialog, DialogContent, DialogTrigger, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { X } from "lucide-react";
import { LazyImage } from './LazyImage';
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";

interface ImageModalProps {
  src: string;
  alt?: string;
  title?: string;
  source?: string;
  children: React.ReactNode;
}

export const ImageModal: React.FC<ImageModalProps> = ({
  src,
  alt,
  title,
  source,
  children
}) => {
  const [isOpen, setIsOpen] = React.useState(false);

  // Fechar modal com tecla ESC
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevenir scroll do body quando modal está aberto
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      
      <DialogContent
        className="max-w-[100vw] w-[100vw] h-[100vh] sm:max-w-[95vw] sm:max-h-[95vh] sm:h-auto sm:w-auto p-0 border-0 bg-black/90 backdrop-blur-lg overflow-hidden"
        onInteractOutside={() => setIsOpen(false)}
      >
        {/* Títulos para acessibilidade */}
        <VisuallyHidden>
          <DialogTitle>{title || "Visualizar imagem"}</DialogTitle>
          <DialogDescription>
            {title ? `Imagem: ${title}` : "Imagem expandida"}
            {source ? ` - Fonte: ${source}` : ""}
          </DialogDescription>
        </VisuallyHidden>

        {/* Container principal */}
        <div className="relative w-full h-full flex items-center justify-center p-4 sm:p-8">

          {/* Botão fechar */}
          <button
            onClick={() => setIsOpen(false)}
            className="absolute top-4 right-4 sm:top-6 sm:right-6 z-50 w-12 h-12 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full flex items-center justify-center text-white transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-white/50"
            aria-label="Fechar imagem"
          >
            <X className="w-6 h-6" />
          </button>

          {/* Container da imagem */}
          <div className="relative max-w-full max-h-full flex flex-col items-center justify-center">

            {/* Imagem principal */}
            <div className="relative flex items-center justify-center max-w-[calc(100vw-2rem)] max-h-[calc(100vh-8rem)] sm:max-w-[calc(95vw-4rem)] sm:max-h-[calc(95vh-8rem)]">
              <img
                src={src}
                alt={alt || title || "Imagem expandida"}
                className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
                style={{ maxWidth: '100%', maxHeight: '100%' }}
              />
            </div>

            {/* Caption da imagem */}
            {(title || source) && (
              <div className="mt-4 sm:mt-6 max-w-2xl mx-auto text-center px-4">
                {title && (
                  <h3 className="text-lg sm:text-xl font-semibold text-white mb-2">
                    {title}
                  </h3>
                )}
                {source && (
                  <p className="text-sm sm:text-base text-gray-300 italic">
                    {source}
                  </p>
                )}
              </div>
            )}
          </div>

          {/* Área clicável para fechar */}
          <div
            className="absolute inset-0 -z-10"
            onClick={() => setIsOpen(false)}
            aria-label="Fechar imagem"
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
