import React, { useEffect } from 'react';
import { Dialog, DialogContent, DialogTrigger, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { X } from "lucide-react";
import { LazyImage } from './LazyImage';
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";

interface ImageModalProps {
  src: string;
  alt?: string;
  title?: string;
  source?: string;
  children: React.ReactNode;
}

export const ImageModal: React.FC<ImageModalProps> = ({
  src,
  alt,
  title,
  source,
  children
}) => {
  const [isOpen, setIsOpen] = React.useState(false);

  // Fechar modal com tecla ESC
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevenir scroll do body quando modal está aberto
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      
      <DialogContent
        className="max-w-[95dvw] w-[95dvw] h-[95dvh] sm:max-w-[90vw] sm:max-h-[90vh] sm:h-auto sm:w-auto p-0 border-0 bg-black/90 backdrop-blur-lg overflow-hidden rounded-lg"
        onInteractOutside={() => setIsOpen(false)}
        hideCloseButton={true}
      >
        {/* Títulos para acessibilidade */}
        <VisuallyHidden>
          <DialogTitle>{title || "Visualizar imagem"}</DialogTitle>
          <DialogDescription>
            {title ? `Imagem: ${title}` : "Imagem expandida"}
            {source ? ` - Fonte: ${source}` : ""}
          </DialogDescription>
        </VisuallyHidden>

        {/* Container com posicionamento relativo */}
        <div className="relative w-full h-full">

          {/* Botão fechar - Fora da área da imagem */}
          <button
            onClick={() => setIsOpen(false)}
            className="absolute top-3 right-3 sm:top-4 sm:right-4 z-50 w-8 h-8 sm:w-10 sm:h-10 bg-white/90 hover:bg-white text-black hover:text-black backdrop-blur-sm rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-white/50 shadow-lg"
            aria-label="Fechar imagem"
          >
            <X className="w-4 h-4 sm:w-5 sm:h-5" />
          </button>

          {/* Container principal */}
          <div
            className="relative w-full h-full flex items-center justify-center p-6 sm:p-8 cursor-pointer"
            onClick={() => setIsOpen(false)}
          >

            {/* Container da imagem */}
            <div
              className="relative max-w-full max-h-full flex flex-col items-center justify-center"
              onClick={(e) => e.stopPropagation()}
            >

              {/* Imagem principal */}
              <div className="relative flex items-center justify-center max-w-[calc(95dvw-3rem)] max-h-[calc(95dvh-8rem)] sm:max-w-[calc(90vw-4rem)] sm:max-h-[calc(90vh-6rem)]">
                <img
                  src={src}
                  alt={alt || title || "Imagem expandida"}
                  className="max-w-full max-h-full object-contain rounded-md sm:rounded-lg shadow-2xl"
                  style={{ maxWidth: '100%', maxHeight: '100%' }}
                />
              </div>

              {/* Caption da imagem */}
              {(title || source) && (
                <div className="mt-3 sm:mt-4 max-w-2xl mx-auto text-center px-4">
                  {title && (
                    <h3 className="text-sm sm:text-base font-semibold text-white mb-1">
                      {title}
                    </h3>
                  )}
                  {source && (
                    <p className="text-xs sm:text-sm text-gray-300 italic">
                      {source}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
