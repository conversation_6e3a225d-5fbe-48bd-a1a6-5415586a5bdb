import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Trigger, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { X, ChevronLeft, ChevronRight } from "lucide-react";
import { LazyImage } from './LazyImage';
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";

interface ImageData {
  src: string;
  alt?: string;
  title?: string;
  source?: string;
}

interface ImageModalProps {
  src: string;
  alt?: string;
  title?: string;
  source?: string;
  children: React.ReactNode;
  allImages?: ImageData[];
  currentIndex?: number;
}

export const ImageModal: React.FC<ImageModalProps> = ({
  src,
  alt,
  title,
  source,
  children,
  allImages = [],
  currentIndex = 0
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeIndex, setActiveIndex] = useState(currentIndex);

  // Se não há array de imagens, criar um com a imagem atual
  const images = allImages.length > 0 ? allImages : [{ src, title, source, alt }];
  const currentImage = images[activeIndex] || images[0];

  // Navegação e controles do modal
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'Escape':
          setIsOpen(false);
          break;
        case 'ArrowLeft':
          if (images.length > 1) {
            setActiveIndex((prev) => (prev > 0 ? prev - 1 : images.length - 1));
          }
          break;
        case 'ArrowRight':
          if (images.length > 1) {
            setActiveIndex((prev) => (prev < images.length - 1 ? prev + 1 : 0));
          }
          break;
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, images.length]);

  // Resetar índice quando modal abre
  useEffect(() => {
    if (isOpen) {
      setActiveIndex(currentIndex);
    }
  }, [isOpen, currentIndex]);

  const goToPrevious = () => {
    setActiveIndex((prev) => (prev > 0 ? prev - 1 : images.length - 1));
  };

  const goToNext = () => {
    setActiveIndex((prev) => (prev < images.length - 1 ? prev + 1 : 0));
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      
      <DialogContent
        className="max-w-[95dvw] w-[95dvw] h-[95dvh] sm:max-w-[90vw] sm:max-h-[90vh] sm:h-auto sm:w-auto p-0 border-0 bg-white dark:bg-gray-900 shadow-2xl overflow-hidden rounded-xl"
        onInteractOutside={() => setIsOpen(false)}
        hideCloseButton={true}
      >
        {/* Títulos para acessibilidade */}
        <VisuallyHidden>
          <DialogTitle>{currentImage.title || "Visualizar imagem"}</DialogTitle>
          <DialogDescription>
            {currentImage.title ? `Imagem: ${currentImage.title}` : "Imagem expandida"}
            {currentImage.source ? ` - Fonte: ${currentImage.source}` : ""}
            {images.length > 1 ? ` - ${activeIndex + 1} de ${images.length}` : ""}
          </DialogDescription>
        </VisuallyHidden>

        {/* Container com posicionamento relativo */}
        <div className="relative w-full h-full bg-white dark:bg-gray-900 flex flex-col">

          {/* Header com botão fechar */}
          <div className="flex-shrink-0 flex justify-end p-3 sm:p-4">
            <button
              onClick={() => setIsOpen(false)}
              className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-600 shadow-md"
              aria-label="Fechar imagem"
            >
              <X className="w-4 h-4 sm:w-5 sm:h-5" />
            </button>
          </div>

          {/* Container principal da imagem */}
          <div className="flex-1 flex items-center justify-center relative">

            {/* Controles de navegação lateral - fora da área da imagem */}
            {images.length > 1 && (
              <>
                {/* Seta esquerda - fora da imagem */}
                <div className="absolute left-2 sm:left-4 top-1/2 -translate-y-1/2 z-10">
                  <button
                    onClick={goToPrevious}
                    className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-600 shadow-md"
                    aria-label="Imagem anterior"
                  >
                    <ChevronLeft className="w-5 h-5 sm:w-6 sm:h-6" />
                  </button>
                </div>

                {/* Seta direita - fora da imagem */}
                <div className="absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 z-10">
                  <button
                    onClick={goToNext}
                    className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-600 shadow-md"
                    aria-label="Próxima imagem"
                  >
                    <ChevronRight className="w-5 h-5 sm:w-6 sm:h-6" />
                  </button>
                </div>
              </>
            )}

            {/* Área da imagem - centralizada com margens para os botões */}
            <div className="flex items-center justify-center px-16 sm:px-20 w-full h-full">
              <div className="relative flex items-center justify-center max-w-full max-h-[calc(95dvh-16rem)] sm:max-h-[calc(90vh-12rem)]">
                <img
                  src={currentImage.src}
                  alt={currentImage.alt || currentImage.title || "Imagem expandida"}
                  className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
                  style={{ maxWidth: '100%', maxHeight: '100%' }}
                />
              </div>
            </div>
          </div>

          {/* Footer com caption e indicadores */}
          <div className="flex-shrink-0 p-4 sm:p-6">
            {/* Caption da imagem */}
            {(currentImage.title || currentImage.source) && (
              <div className="max-w-2xl mx-auto text-center mb-4">
                {currentImage.title && (
                  <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                    {currentImage.title}
                  </h3>
                )}
                {currentImage.source && (
                  <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 italic">
                    {currentImage.source}
                  </p>
                )}
                {images.length > 1 && (
                  <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mt-2">
                    {activeIndex + 1} de {images.length}
                  </p>
                )}
              </div>
            )}

            {/* Indicadores de posição */}
            {images.length > 1 && (
              <div className="flex justify-center gap-2">
                {images.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveIndex(index)}
                    className={`w-2 h-2 rounded-full transition-all duration-200 ${
                      index === activeIndex
                        ? 'bg-blue-500 dark:bg-blue-400 w-6'
                        : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
                    }`}
                    aria-label={`Ir para imagem ${index + 1}`}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
