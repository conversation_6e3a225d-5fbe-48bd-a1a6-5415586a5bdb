import React, { useEffect } from 'react';
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { X } from "lucide-react";
import { LazyImage } from './LazyImage';

interface ImageModalProps {
  src: string;
  alt?: string;
  title?: string;
  source?: string;
  children: React.ReactNode;
}

export const ImageModal: React.FC<ImageModalProps> = ({
  src,
  alt,
  title,
  source,
  children
}) => {
  const [isOpen, setIsOpen] = React.useState(false);

  // Fechar modal com tecla ESC
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevenir scroll do body quando modal está aberto
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      
      <DialogContent 
        className="max-w-none w-full h-full sm:max-w-[90vw] sm:max-h-[90vh] sm:h-auto sm:w-auto p-0 border-0 bg-transparent shadow-none"
        onInteractOutside={() => setIsOpen(false)}
      >
        {/* Background overlay */}
        <div className="fixed inset-0 bg-black/80 backdrop-blur-md sm:backdrop-blur-lg">
          
          {/* Container principal */}
          <div className="relative w-full h-full flex items-center justify-center p-4 sm:p-6">
            
            {/* Botão fechar */}
            <button
              onClick={() => setIsOpen(false)}
              className="absolute top-4 right-4 sm:top-6 sm:right-6 z-50 w-10 h-10 sm:w-12 sm:h-12 bg-black/50 hover:bg-black/70 backdrop-blur-sm rounded-full flex items-center justify-center text-white transition-all duration-200 hover:scale-110"
              aria-label="Fechar imagem"
            >
              <X className="w-5 h-5 sm:w-6 sm:h-6" />
            </button>

            {/* Container da imagem */}
            <div className="relative max-w-full max-h-full flex flex-col items-center">
              
              {/* Imagem principal */}
              <div className="relative max-w-full max-h-[calc(100dvh-120px)] sm:max-h-[calc(90vh-120px)] flex items-center justify-center">
                <LazyImage
                  src={src}
                  alt={alt || title || "Imagem expandida"}
                  className="max-w-full max-h-full object-contain rounded-lg sm:rounded-xl shadow-2xl"
                />
              </div>

              {/* Caption da imagem */}
              {(title || source) && (
                <div className="mt-4 sm:mt-6 max-w-2xl mx-auto text-center px-4">
                  {title && (
                    <h3 className="text-lg sm:text-xl font-semibold text-white mb-2">
                      {title}
                    </h3>
                  )}
                  {source && (
                    <p className="text-sm sm:text-base text-gray-300 italic">
                      {source}
                    </p>
                  )}
                </div>
              )}
            </div>

            {/* Área clicável para fechar (mobile) */}
            <div 
              className="absolute inset-0 -z-10 sm:hidden"
              onClick={() => setIsOpen(false)}
              aria-label="Fechar imagem"
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
