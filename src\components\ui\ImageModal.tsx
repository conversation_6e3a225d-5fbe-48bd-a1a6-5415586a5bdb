import React, { useEffect } from 'react';
import { Dialog, DialogContent, DialogTrigger, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { X } from "lucide-react";
import { LazyImage } from './LazyImage';
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";

interface ImageModalProps {
  src: string;
  alt?: string;
  title?: string;
  source?: string;
  children: React.ReactNode;
}

export const ImageModal: React.FC<ImageModalProps> = ({
  src,
  alt,
  title,
  source,
  children
}) => {
  const [isOpen, setIsOpen] = React.useState(false);

  // Fechar modal com tecla ESC
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevenir scroll do body quando modal está aberto
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      
      <DialogContent
        className="max-w-[100dvw] w-[100dvw] h-[100dvh] sm:max-w-[95vw] sm:max-h-[95vh] sm:h-auto sm:w-auto p-0 border-0 bg-black/90 backdrop-blur-lg overflow-hidden"
        onInteractOutside={() => setIsOpen(false)}
        hideCloseButton={true}
      >
        {/* Títulos para acessibilidade */}
        <VisuallyHidden>
          <DialogTitle>{title || "Visualizar imagem"}</DialogTitle>
          <DialogDescription>
            {title ? `Imagem: ${title}` : "Imagem expandida"}
            {source ? ` - Fonte: ${source}` : ""}
          </DialogDescription>
        </VisuallyHidden>

        {/* Botão fechar - Único e limpo */}
        <button
          onClick={() => setIsOpen(false)}
          className="absolute top-4 right-4 sm:top-6 sm:right-6 z-50 w-10 h-10 sm:w-12 sm:h-12 bg-black/60 hover:bg-black/80 backdrop-blur-sm rounded-full flex items-center justify-center text-white transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-white/50 shadow-lg"
          aria-label="Fechar imagem"
        >
          <X className="w-5 h-5 sm:w-6 sm:h-6" />
        </button>

        {/* Container principal */}
        <div
          className="relative w-full h-full flex items-center justify-center p-4 sm:p-8 cursor-pointer"
          onClick={() => setIsOpen(false)}
        >

          {/* Container da imagem */}
          <div
            className="relative max-w-full max-h-full flex flex-col items-center justify-center"
            onClick={(e) => e.stopPropagation()}
          >

            {/* Imagem principal */}
            <div className="relative flex items-center justify-center max-w-[calc(100dvw-2rem)] max-h-[calc(100dvh-12rem)] sm:max-w-[calc(95vw-4rem)] sm:max-h-[calc(95vh-8rem)]">
              <img
                src={src}
                alt={alt || title || "Imagem expandida"}
                className="max-w-full max-h-full object-contain rounded-md sm:rounded-lg shadow-2xl"
                style={{ maxWidth: '100%', maxHeight: '100%' }}
              />
            </div>

            {/* Caption da imagem */}
            {(title || source) && (
              <div className="mt-3 sm:mt-4 max-w-2xl mx-auto text-center px-4">
                {title && (
                  <h3 className="text-base sm:text-lg font-semibold text-white mb-1 sm:mb-2">
                    {title}
                  </h3>
                )}
                {source && (
                  <p className="text-xs sm:text-sm text-gray-300 italic">
                    {source}
                  </p>
                )}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
