import React, { useEffect, useState, useRef, useCallback } from "react";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import { useForm } from "react-hook-form";
import { toast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useQueryClient, useQuery } from "@tanstack/react-query";
import { slugify } from "@/utils/slugify";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useAuth } from "@/hooks/useAuth";
import { ImagePlus, Loader2, Bold, Italic, Link as LinkIcon, List, ListOrdered, Image } from "lucide-react";
import { ImageUploadModal } from "./ImageUploadModal";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Link from '@tiptap/extension-link';
import Underline from '@tiptap/extension-underline';
import TiptapImage from '@tiptap/extension-image';
import { ContentEditor } from './editor/ContentEditor';
import { ContentStructure } from './editor/types';
type PointerDownOutsideEvent = CustomEvent<{
  originalEvent: PointerEvent;
}>;

const topicSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  description: z.string().optional(),
  slug: z.string().optional(),
  icon: z.string().optional(),
  category_id: z.string().uuid("Categoria inválida"),
  parent_id: z.string().optional(),
  image_url: z.string().optional(),
  summary_title: z.string().optional(),
  summary_content: z.string().optional(),
  format_type: z.enum(["standard", "simple", "optimized"]).default("standard"),
  conducts_content: z.string().optional(),
  treatment_content: z.string().optional(),
  has_treatment: z.boolean().default(false),
  published: z.boolean().default(true),
});

type TopicFormData = z.infer<typeof topicSchema>;

interface TopicDialogProps {
  topic?: {
    id: string;
    name: string;
    description: string | null;
    slug: string;
    icon: string | null;
    category_id: string;
    parent_id?: string | null;
    is_subcategory?: boolean | null;
    image_url?: string | null;
    published?: boolean;
  };
  categoryId: string;
  onOpenChange: (open: boolean) => void;
  open: boolean;

}

const MenuBar = ({ editor }: { editor: any }) => {
  if (!editor) {
    return null;
  }

  const handleButtonClick = (callback: () => void) => (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent form submission
    e.stopPropagation(); // Prevent event bubbling
    callback();
  };

  const setLink = () => {
    const url = window.prompt('URL:')
    if (url) {
      editor.chain().focus().setLink({ href: url }).run()
    }
  }

  const addImage = () => {
    const url = window.prompt('URL da imagem:')
    if (url) {
      editor.chain().focus().setImage({ src: url }).run()
    }
  }

  return (
    <div className="flex items-center gap-1 border-b p-2">
      <Button
        type="button" // Explicitly set type to button
        variant="ghost"
        size="sm"
        onClick={handleButtonClick(() => editor.chain().focus().toggleBold().run())}
        className={editor.isActive('bold') ? 'bg-muted' : ''}
      >
        <Bold className="h-4 w-4" />
      </Button>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={handleButtonClick(() => editor.chain().focus().toggleItalic().run())}
        className={editor.isActive('italic') ? 'bg-muted' : ''}
      >
        <Italic className="h-4 w-4" />
      </Button>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={handleButtonClick(setLink)}
        className={editor.isActive('link') ? 'bg-muted' : ''}
      >
        <LinkIcon className="h-4 w-4" />
      </Button>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={handleButtonClick(() => editor.chain().focus().toggleBulletList().run())}
        className={editor.isActive('bulletList') ? 'bg-muted' : ''}
      >
        <List className="h-4 w-4" />
      </Button>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={handleButtonClick(() => editor.chain().focus().toggleOrderedList().run())}
        className={editor.isActive('orderedList') ? 'bg-muted' : ''}
      >
        <ListOrdered className="h-4 w-4" />
      </Button>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={handleButtonClick(addImage)}
      >
        <Image className="h-4 w-4" />
      </Button>
    </div>
  );
};

export function TopicDialog({ topic, categoryId, onOpenChange, open }: TopicDialogProps) {
  console.log('🔄 TopicDialog renderizado', { isEditing: !!topic?.id, categoryId, open });

  const queryClient = useQueryClient();
  const { user } = useAuth();

  // Buscar subcategorias da categoria atual
  const { data: subcategories } = useQuery({
    queryKey: ['subcategories', categoryId],
    queryFn: async () => {
      if (!categoryId) return [];

      const { data, error } = await supabase
        .from('pedbook_conducts_topics')
        .select('id, name')
        .eq('category_id', categoryId)
        .eq('is_subcategory', true)
        .is('parent_id', null)
        .order('name');

      if (error) throw error;
      return data || [];
    },
    enabled: !!categoryId
  });
  const [uploading, setUploading] = useState(false);
  const [activeTab, setActiveTab] = useState("basic");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const dialogRef = useRef<HTMLDivElement>(null);

  const formRef = useRef<ReturnType<typeof useForm<TopicFormData>>>(null);

  const form = useForm<TopicFormData>({
    resolver: zodResolver(topicSchema),
    defaultValues: {
      name: "",
      description: "",
      slug: "",
      icon: "",
      category_id: categoryId,
      parent_id: "none",
      image_url: "",
      summary_title: "",
      summary_content: "",
      format_type: "standard",
      published: true,
      conducts_content: "",
      treatment_content: "",
      has_treatment: false,
    },
    mode: 'onSubmit', // Importante: só valida no submit
  });

  formRef.current = form;

  const editor = useEditor({
    extensions: [
      StarterKit,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-500 underline'
        }
      }),
      Underline,
      TiptapImage.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg my-4',
        },
      }),
    ],
    content: '',
    onUpdate: useCallback(({ editor }) => {
      const content = editor.getHTML();
      formRef.current?.setValue('summary_content', content, {
        shouldDirty: false,
        shouldTouch: true,
        shouldValidate: false,
      });
    }, []),
  });

  useEffect(() => {
    if (open) {
      if (topic) {
        console.log('🔄 Carregando dados do tópico para edição:', topic.id);
        form.reset({
          name: topic.name,
          description: topic.description || "",
          slug: topic.slug,
          icon: topic.icon || "",
          category_id: topic.category_id,
          parent_id: topic.parent_id || "none",
          image_url: topic.image_url || "",
          summary_title: "",
          summary_content: "",
          format_type: "standard",
          published: topic.published || true,
          conducts_content: "",
          treatment_content: "",
          has_treatment: false,
        });
      } else {
        console.log('🆕 Preparando formulário para novo tópico');
        form.reset({
          name: "",
          description: "",
          slug: "",
          icon: "",
          category_id: categoryId,
          parent_id: "none",
          image_url: "",
          summary_title: "",
          summary_content: "",
          format_type: "standard",
          published: true,
          conducts_content: "",
          treatment_content: "",
          has_treatment: false,
        });
      }
      setActiveTab("basic");
    }
  }, [open, topic, categoryId, form]);

  useEffect(() => {
    if (topic?.id) {
      const loadSummary = async () => {
        // Primeiro, tentar carregar conteúdo otimizado
        const { data: optimizedData, error: optimizedError } = await supabase
          .from('pedbook_conducts_optimized')
          .select('*')
          .eq('topic_id', topic.id)
          .maybeSingle();

        if (optimizedData) {
          // Carregar dados otimizados
          form.setValue('summary_title', optimizedData.title);
          form.setValue('format_type', 'optimized');
          form.setValue('conducts_content', optimizedData.conducts_content || '');
          form.setValue('treatment_content', optimizedData.treatment_content || '');
          form.setValue('has_treatment', optimizedData.has_treatment || false);
          form.setValue('published', optimizedData.published || false);
          return;
        }

        // Se não encontrou otimizado, carregar formato tradicional
        const { data: summaryData, error } = await supabase
          .from('pedbook_conducts_summaries')
          .select('*')
          .eq('topic_id', topic.id)
          .maybeSingle();

        if (summaryData) {
          form.setValue('summary_title', summaryData.title);
          form.setValue('summary_content', summaryData.content as string);
          form.setValue('format_type', summaryData.format_type as "standard" | "simple" || "standard");
          form.setValue('published', summaryData.published || false);
          editor?.commands.setContent(summaryData.content as string);
        }
      };

      loadSummary();
    }
  }, [topic?.id, form, editor]);

  useEffect(() => {
    if (categoryId) {
      form.setValue('category_id', categoryId);
    }
  }, [categoryId, form]);

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      if (!event.target.files || event.target.files.length === 0) {
        return;
      }

      const file = event.target.files[0];
      setUploading(true);

      const objectUrl = URL.createObjectURL(file);
      const imgElement = document.createElement('img');
      await new Promise((resolve, reject) => {
        imgElement.onload = resolve;
        imgElement.onerror = reject;
        imgElement.src = objectUrl;
      });

      const canvas = document.createElement('canvas');
      canvas.width = imgElement.width;
      canvas.height = imgElement.height;
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        throw new Error("Could not get canvas context");
      }

      ctx.drawImage(imgElement, 0, 0);
      URL.revokeObjectURL(objectUrl);

      const webpBlob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => resolve(blob!), 'image/webp', 0.8);
      });

      const fileName = `${crypto.randomUUID()}.webp`;
      const { data, error: uploadError } = await supabase.storage
        .from('category-icons')
        .upload(fileName, webpBlob);

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('category-icons')
        .getPublicUrl(fileName);

      // Verifica a origem do upload pela ID do input
      if (event.target.id === 'image-upload') {
        // Atualiza apenas o campo image_url para o logo
        form.setValue('image_url', publicUrl);
      } else if (event.target.id === 'image-upload-content' && editor) {
        // Insere a imagem no editor apenas para o conteúdo (método antigo)
        editor.chain().focus().setImage({ src: publicUrl }).run();
      }

      toast({ title: "Imagem enviada com sucesso" });
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: "Erro ao enviar imagem",
        description: "Ocorreu um erro ao fazer upload da imagem",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  // Nova função para inserir imagem com metadados
  const handleImageWithCaptionInsert = (markdownText: string) => {
    if (editor) {
      editor.chain().focus().insertContent(markdownText + '\n\n').run();
    }
  };

  // Função para inserir imagem em textarea (formato otimizado)
  const handleImageInsertInTextarea = (markdownText: string, fieldName: 'conducts_content' | 'treatment_content') => {
    const currentValue = form.getValues(fieldName) || '';
    const newValue = currentValue + '\n\n' + markdownText + '\n\n';
    form.setValue(fieldName, newValue);
  };

  const handleContentStructureChange = useCallback((structure: ContentStructure) => {
    let markdownContent = '';

    structure.sections.forEach(section => {
      markdownContent += `<h2><strong>##. ${section.content}</strong></h2>\n\n`;

      section.children.forEach(subsection => {
        markdownContent += `<h3><strong>##; ${subsection.content}</strong></h3>\n\n`;
      });
    });

    editor?.commands.setContent(markdownContent);
    formRef.current?.setValue('summary_content', markdownContent, {
      shouldDirty: false,
      shouldTouch: true,
      shouldValidate: false,
    });
  }, [editor?.commands]);

  const handleClose = () => {
    // Resetar estado do formulário
    setActiveTab("basic");
    form.reset();
    onOpenChange(false);
  };

  const onSubmit = async (data: TopicFormData) => {
    try {
      console.log('🚀 onSubmit chamado', { isSubmitting });

      if (isSubmitting) {
        console.log('🚫 Submissão já em andamento, ignorando...');
        return;
      }

      setIsSubmitting(true);

      if (!user) {
        toast({
          title: "Erro de autenticação",
          description: "Você precisa estar autenticado para realizar esta ação",
          variant: "destructive",
        });
        return;
      }

      if (!data.category_id) {
        throw new Error("Categoria é obrigatória");
      }

      const slugValue = data.slug || slugify(data.name);

      const payload = {
        name: data.name,
        description: data.description,
        slug: slugValue,
        icon: data.icon,
        category_id: data.category_id,
        parent_id: data.parent_id && data.parent_id !== "none" ? data.parent_id : null,
        is_subcategory: false,
        image_url: data.image_url,
        published: data.published
      };

      let topicId = topic?.id;

      if (topic?.id) {
        const { error } = await supabase
          .from('pedbook_conducts_topics')
          .update(payload)
          .eq('id', topic.id);

        if (error) throw error;
        toast({ title: "Tópico atualizado com sucesso" });
      } else {
        const { data: newTopic, error } = await supabase
          .from('pedbook_conducts_topics')
          .insert([payload])
          .select()
          .single();

        if (error) throw error;
        topicId = newTopic.id;
        toast({ title: "Tópico criado com sucesso" });
      }

      // Salvar conteúdo baseado no formato
      if (data.format_type === 'optimized') {
        // Salvar no formato otimizado
        if (data.conducts_content || data.treatment_content) {
          const optimizedPayload = {
            topic_id: topicId,
            title: data.summary_title || data.name,
            slug: slugValue,
            conducts_content: data.conducts_content || '',
            treatment_content: data.treatment_content || '',
            format_type: 'optimized',
            has_treatment: data.has_treatment || false,
            published: data.published
          };

          const { error: optimizedError } = await supabase
            .from('pedbook_conducts_optimized')
            .upsert([optimizedPayload], {
              onConflict: 'topic_id'
            });

          if (optimizedError) throw optimizedError;
        }
      } else {
        // Salvar no formato tradicional
        if (data.summary_content) {
          const summaryPayload = {
            topic_id: topicId,
            title: data.summary_title || data.name,
            content: data.summary_content,
            content_type: 'markdown',
            format_type: data.format_type,
            slug: slugValue,
            published: data.published
          };

          const { error: summaryError } = await supabase
            .from('pedbook_conducts_summaries')
            .upsert([summaryPayload], {
              onConflict: 'topic_id'
            });

          if (summaryError) throw summaryError;
        }
      }

      queryClient.invalidateQueries({ queryKey: ['conducts-topics'] });

      // Mostrar toast de sucesso
      toast({
        title: "Sucesso",
        description: topic?.id ? "Tópico atualizado com sucesso!" : "Tópico criado com sucesso!",
      });

      // Fechar dialog
      handleClose();
    } catch (error) {
      console.error('❌ Erro ao salvar tópico:', error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao salvar o tópico",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };




  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        if (!newOpen) {
          handleClose();
        }
      }}
    >
      <DialogContent
        ref={dialogRef}
        className="max-w-3xl"
      >
        <DialogHeader>
          <DialogTitle>{topic ? "Editar Tópico" : "Novo Tópico"}</DialogTitle>
          <DialogDescription>
            {topic ? "Edite os detalhes do tópico" : "Crie um novo tópico"}
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="basic">Informações Básicas</TabsTrigger>
            <TabsTrigger value="summary">Resumo</TabsTrigger>
          </TabsList>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <TabsContent value="basic">
                <ScrollArea className="h-[70dvh] pr-4">
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Nome</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Descrição</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="slug"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Slug</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="icon"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Ícone</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="image_url"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Imagem</FormLabel>
                          <FormControl>
                            <div className="flex items-center gap-4">
                              <Input
                                type="file"
                                accept="image/*"
                                onChange={handleImageUpload}
                                className="hidden"
                                id="image-upload"
                              />
                              <label
                                htmlFor="image-upload"
                                className="flex items-center gap-2 px-4 py-2 border rounded-md cursor-pointer hover:bg-gray-50"
                              >
                                {uploading ? (
                                  <Loader2 className="w-4 h-4 animate-spin" />
                                ) : (
                                  <ImagePlus className="w-4 h-4" />
                                )}
                                {field.value ? "Trocar imagem" : "Adicionar imagem"}
                              </label>
                              {field.value && (
                                <img
                                  src={field.value}
                                  alt="Preview"
                                  className="w-16 h-16 object-cover rounded-md"
                                />
                              )}
                              <Input type="hidden" {...field} />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Campo de Subcategoria Pai */}
                    <div className="border-t pt-4 space-y-4">
                      <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300">Localização</h4>

                      <FormField
                        control={form.control}
                        name="parent_id"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Subcategoria Pai (opcional)</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Selecione uma subcategoria pai" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="none">Nenhuma (tópico direto na categoria)</SelectItem>
                                {subcategories?.map((subcategory) => (
                                  <SelectItem key={subcategory.id} value={subcategory.id}>
                                    📁 {subcategory.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              Se selecionado, este tópico ficará dentro da subcategoria
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </ScrollArea>
              </TabsContent>

              <TabsContent value="summary">
                <ScrollArea className="h-[70dvh] pr-4">
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="summary_title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Título do Resumo</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="format_type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Formato do Conteúdo</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione o formato" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="standard">Padrão (Editor Completo)</SelectItem>
                              <SelectItem value="simple">Simples (Marcadores ##. e ##;)</SelectItem>
                              <SelectItem value="optimized">Otimizado (Condutas + Tratamento)</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="published"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Publicar Resumo
                            </FormLabel>
                            <div className="text-sm text-muted-foreground">
                              Tornar este resumo visível na listagem pública
                            </div>
                          </div>
                          <FormControl>
                            <input
                              type="checkbox"
                              checked={field.value}
                              onChange={field.onChange}
                              className="h-4 w-4 rounded border-gray-300"
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    {form.watch('format_type') === 'optimized' ? (
                      <>
                        <FormField
                          control={form.control}
                          name="conducts_content"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Conteúdo de Condutas e Manejos</FormLabel>
                              <FormControl>
                                <div>
                                  <textarea
                                    {...field}
                                    className="min-h-[300px] w-full rounded-md border border-input bg-white dark:bg-slate-800 px-3 py-2 text-sm text-foreground dark:text-gray-100 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700"
                                    placeholder="Digite o conteúdo de condutas e manejos em markdown..."
                                  />
                                  {/* Botões de upload de imagem */}
                                  <div className="border-t p-2 flex gap-2 bg-gray-50 dark:bg-gray-800 rounded-b-md">
                                    <ImageUploadModal onImageInserted={(markdown) => handleImageInsertInTextarea(markdown, 'conducts_content')}>
                                      <Button variant="outline" size="sm" type="button">
                                        <ImagePlus className="w-4 h-4 mr-2" />
                                        Adicionar Imagem
                                      </Button>
                                    </ImageUploadModal>
                                  </div>
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="has_treatment"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">
                                  Incluir Tratamento
                                </FormLabel>
                                <div className="text-sm text-muted-foreground">
                                  Ativar seção de tratamento para este tópico
                                </div>
                              </div>
                              <FormControl>
                                <input
                                  type="checkbox"
                                  checked={field.value}
                                  onChange={field.onChange}
                                  className="h-4 w-4 rounded border-gray-300"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        {form.watch('has_treatment') && (
                          <FormField
                            control={form.control}
                            name="treatment_content"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Conteúdo de Tratamento</FormLabel>
                                <FormControl>
                                  <div>
                                    <textarea
                                      {...field}
                                      className="min-h-[300px] w-full rounded-md border border-input bg-white dark:bg-slate-800 px-3 py-2 text-sm text-foreground dark:text-gray-100 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700"
                                      placeholder="Digite o conteúdo de tratamento em markdown..."
                                    />
                                    {/* Botões de upload de imagem */}
                                    <div className="border-t p-2 flex gap-2 bg-gray-50 dark:bg-gray-800 rounded-b-md">
                                      <ImageUploadModal onImageInserted={(markdown) => handleImageInsertInTextarea(markdown, 'treatment_content')}>
                                        <Button variant="outline" size="sm" type="button">
                                          <ImagePlus className="w-4 h-4 mr-2" />
                                          Adicionar Imagem
                                        </Button>
                                      </ImageUploadModal>
                                    </div>
                                  </div>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        )}
                      </>
                    ) : (
                      <FormField
                        control={form.control}
                        name="summary_content"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Conteúdo do Resumo</FormLabel>
                            <FormControl>
                              <div className="min-h-[400px] rounded-md border-[0.5px]">
                                <MenuBar editor={editor} />
                                <div className="p-3">
                                  <ContentEditor
                                    onChange={handleContentStructureChange}
                                  />
                                  <EditorContent editor={editor} className="min-h-[350px] prose max-w-none mt-4" />
                                </div>
                                <input type="hidden" {...field} />
                                <div className="border-t p-2 flex gap-2">
                                  {/* Botão antigo para imagem simples */}
                                  <label
                                    htmlFor="image-upload-content"
                                    className="flex items-center gap-2 px-3 py-2 border rounded-md cursor-pointer hover:bg-gray-50 text-sm"
                                  >
                                    {uploading ? (
                                      <Loader2 className="w-4 h-4 animate-spin" />
                                    ) : (
                                      <Image className="w-4 h-4" />
                                    )}
                                    Imagem simples
                                  </label>
                                  <input
                                    type="file"
                                    accept="image/*"
                                    onChange={handleImageUpload}
                                    className="hidden"
                                    id="image-upload-content"
                                  />

                                  {/* Novo modal para imagem com título e fonte */}
                                  <ImageUploadModal onImageInserted={handleImageWithCaptionInsert}>
                                    <Button variant="outline" size="sm" type="button">
                                      <ImagePlus className="w-4 h-4 mr-2" />
                                      Imagem com título
                                    </Button>
                                  </ImageUploadModal>
                                </div>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>



              <div className="flex justify-end pt-4">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Salvando...
                    </>
                  ) : (
                    topic ? "Salvar" : "Criar"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
